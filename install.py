#!/usr/bin/env python3
"""
LocalMind AI System Installation Script

This script sets up the LocalMind AI system with all dependencies and initial configuration.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 11):
        print("❌ Error: Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")

def check_system_requirements():
    """Check system requirements."""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    check_python_version()
    
    # Check available memory
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 4:
            print(f"⚠️  Warning: Low memory detected ({memory_gb:.1f}GB). Recommended: 8GB+")
        else:
            print(f"✅ Memory: {memory_gb:.1f}GB")
    except ImportError:
        print("ℹ️  Could not check memory (psutil not installed)")
    
    # Check disk space
    try:
        disk_usage = psutil.disk_usage('/')
        free_gb = disk_usage.free / (1024**3)
        if free_gb < 5:
            print(f"⚠️  Warning: Low disk space ({free_gb:.1f}GB free). Recommended: 10GB+")
        else:
            print(f"✅ Disk space: {free_gb:.1f}GB free")
    except:
        print("ℹ️  Could not check disk space")

def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        
        print("✅ Dependencies installed successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("\nTrying alternative installation methods...")
        
        # Try installing core dependencies individually
        core_deps = [
            "torch",
            "transformers",
            "sentence-transformers",
            "chromadb",
            "fastapi",
            "streamlit",
            "sqlalchemy",
            "aiosqlite",
            "pydantic",
            "click",
            "rich",
            "loguru",
            "python-dotenv",
            "pyyaml"
        ]
        
        for dep in core_deps:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
                print(f"✅ Installed {dep}")
            except subprocess.CalledProcessError:
                print(f"⚠️  Failed to install {dep}")

def setup_directories():
    """Create necessary directories."""
    print("\n📁 Setting up directories...")
    
    directories = [
        "data",
        "data/logs",
        "data/models",
        "data/sessions",
        "data/knowledge",
        "data/plugins"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def initialize_system():
    """Initialize the LocalMind system."""
    print("\n🚀 Initializing LocalMind AI system...")
    
    try:
        # Run the setup script
        subprocess.run([sys.executable, "-m", "localmind.setup", "--init"], check=True)
        print("✅ System initialized successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error initializing system: {e}")
        print("You can try running the initialization manually:")
        print("python -m localmind.setup --init")

def run_tests():
    """Run basic tests to verify installation."""
    print("\n🧪 Running basic tests...")
    
    try:
        # Try importing the main module
        import localmind
        print("✅ LocalMind module imported successfully")
        
        # Try running a simple test
        subprocess.run([sys.executable, "-m", "pytest", "tests/test_localmind.py::TestConfiguration::test_config_creation", "-v"], 
                      check=True, capture_output=True)
        print("✅ Basic tests passed")
        
    except ImportError as e:
        print(f"❌ Error importing LocalMind: {e}")
    except subprocess.CalledProcessError:
        print("⚠️  Some tests failed, but installation may still be functional")
    except FileNotFoundError:
        print("ℹ️  pytest not available, skipping tests")

def show_usage_instructions():
    """Show usage instructions."""
    print("\n🎉 Installation completed!")
    print("\n" + "="*60)
    print("LocalMind AI System - Usage Instructions")
    print("="*60)
    
    print("\n🖥️  Command Line Interface:")
    print("   python -m localmind.cli")
    print("   python -m localmind.cli --query 'Hello, how are you?'")
    print("   python -m localmind.cli --code 'Create a sorting function'")
    
    print("\n🌐 Web Interface:")
    print("   python -m localmind.web")
    print("   Then open: http://localhost:8000")
    
    print("\n🔧 System Management:")
    print("   python -m localmind.setup --init    # Re-initialize system")
    print("   python -m localmind.setup --help    # Show setup options")
    
    print("\n📚 API Usage:")
    print("   from localmind import LocalMindAPI")
    print("   api = LocalMindAPI()")
    print("   response = await api.chat('Hello!')")
    
    print("\n📖 Documentation:")
    print("   See README.md for detailed documentation")
    print("   Configuration: config.yaml")
    print("   Data directory: ./data/")
    
    print("\n⚠️  Important Notes:")
    print("   - First run may take time to download AI models")
    print("   - System operates completely offline after setup")
    print("   - All data is stored locally in the ./data/ directory")
    
    print("\n🆘 Troubleshooting:")
    print("   - Check logs in ./data/logs/")
    print("   - Run tests: python -m pytest tests/")
    print("   - Reset system: rm -rf data/ && python install.py")

def main():
    """Main installation function."""
    print("🧠 LocalMind AI System Installation")
    print("="*40)
    
    # Check if we're in the right directory
    if not Path("requirements.txt").exists():
        print("❌ Error: requirements.txt not found")
        print("Please run this script from the LocalMind project directory")
        sys.exit(1)
    
    try:
        # Step 1: Check system requirements
        check_system_requirements()
        
        # Step 2: Install dependencies
        install_dependencies()
        
        # Step 3: Setup directories
        setup_directories()
        
        # Step 4: Initialize system
        initialize_system()
        
        # Step 5: Run tests
        run_tests()
        
        # Step 6: Show usage instructions
        show_usage_instructions()
        
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during installation: {e}")
        print("Please check the error message and try again")
        sys.exit(1)

if __name__ == "__main__":
    main()
