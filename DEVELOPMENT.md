# LocalMind AI System - Development Guide

## Development Environment Setup

### Prerequisites
- Python 3.11 or higher
- Git
- 8GB+ RAM recommended
- 10GB+ free disk space

### Development Installation
```bash
# Clone the repository
git clone <repository-url>
cd localmind

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # If available

# Install in development mode
pip install -e .

# Initialize the system
python install.py
```

### Development Tools
```bash
# Code formatting
black localmind/
isort localmind/

# Linting
flake8 localmind/
pylint localmind/

# Type checking
mypy localmind/

# Testing
pytest tests/ -v
pytest tests/ --cov=localmind --cov-report=html
```

## Project Structure

```
localmind/
├── 📁 localmind/           # Main package
│   ├── 🧠 core/            # Core AI engine
│   ├── 📚 knowledge/       # Knowledge management
│   ├── 💻 execution/       # Code generation/execution
│   ├── 🔧 extension/       # Self-extension framework
│   ├── 🌐 interfaces/      # User interfaces
│   ├── 💾 storage/         # Data persistence
│   └── 🛠️ utils/           # Utilities
├── 🧪 tests/              # Test suite
├── 📜 scripts/            # Utility scripts
├── 📖 docs/               # Documentation
└── 🗂️ data/               # Runtime data (created on first run)
```

## Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-capability

# Develop feature
# - Write code in appropriate module
# - Add comprehensive tests
# - Update documentation
# - Follow coding standards

# Test thoroughly
python -m pytest tests/
python run_localmind.py test

# Commit changes
git add .
git commit -m "feat: add new capability"
```

### 2. Testing Strategy
```bash
# Unit tests
pytest tests/test_specific_module.py

# Integration tests
pytest tests/test_localmind.py

# Performance tests
python scripts/system_monitor.py --once

# Manual testing
python demo.py
python run_localmind.py cli
```

### 3. Code Quality
```bash
# Format code
black localmind/ tests/
isort localmind/ tests/

# Check style
flake8 localmind/

# Type checking
mypy localmind/

# Security check
bandit -r localmind/
```

## Adding New Features

### 1. Adding a New AI Capability
```python
# Example: Adding sentiment analysis

# 1. Create module in appropriate package
# localmind/core/sentiment_analyzer.py

class SentimentAnalyzer:
    """Analyzes sentiment of text."""
    
    def __init__(self):
        self.model = None
    
    async def initialize(self):
        """Initialize sentiment model."""
        # Load or create sentiment model
        pass
    
    async def analyze_sentiment(self, text: str) -> dict:
        """Analyze sentiment of text."""
        # Implementation
        return {"sentiment": "positive", "confidence": 0.8}

# 2. Integrate with main engine
# localmind/core/ai_engine.py

class LocalMindEngine:
    def __init__(self):
        # ... existing code ...
        self.sentiment_analyzer = SentimentAnalyzer()
    
    async def initialize(self):
        # ... existing code ...
        await self.sentiment_analyzer.initialize()

# 3. Add API endpoint
# localmind/interfaces/api.py

class LocalMindAPI:
    async def analyze_sentiment(self, text: str) -> dict:
        """Analyze sentiment of text."""
        return await self.engine.sentiment_analyzer.analyze_sentiment(text)

# 4. Add tests
# tests/test_sentiment_analyzer.py

class TestSentimentAnalyzer:
    @pytest.mark.asyncio
    async def test_sentiment_analysis(self):
        analyzer = SentimentAnalyzer()
        await analyzer.initialize()
        result = await analyzer.analyze_sentiment("I love this!")
        assert result["sentiment"] in ["positive", "negative", "neutral"]
```

### 2. Adding a New Plugin
```python
# Create plugin file: localmind/extension/plugins/my_plugin.py

from localmind.extension.plugin_engine import Plugin

class MyCustomPlugin(Plugin):
    """Custom plugin for specific functionality."""
    
    def __init__(self):
        super().__init__("my_custom_plugin", "1.0.0")
        self.metadata = {
            "description": "Custom functionality plugin",
            "author": "Developer Name"
        }
    
    async def initialize(self):
        """Initialize plugin."""
        # Setup code here
        pass
    
    def get_capabilities(self):
        """Return plugin capabilities."""
        return ["custom_feature", "special_processing"]
    
    async def hook_before_chat(self, message: str, context: dict) -> dict:
        """Process message before chat."""
        # Custom processing
        return {"processed_message": message}
    
    async def hook_after_chat(self, message: str, response: str, context: dict) -> dict:
        """Process after chat response."""
        # Custom analysis
        return {"analysis": "custom_analysis_result"}
```

### 3. Adding a New Interface
```python
# Example: Adding REST API interface
# localmind/interfaces/rest_api.py

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

app = FastAPI(title="LocalMind AI API")

class ChatRequest(BaseModel):
    message: str
    context: dict = {}

class ChatResponse(BaseModel):
    response: str
    timestamp: str

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Chat endpoint."""
    try:
        api = LocalMindAPI()
        await api.initialize()
        response = await api.chat(request.message, request.context)
        return ChatResponse(
            response=response,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## Testing Guidelines

### 1. Test Structure
```python
# tests/test_new_feature.py

import pytest
import asyncio
import tempfile
from pathlib import Path

from localmind.core.new_feature import NewFeature

@pytest.fixture
async def temp_config():
    """Create temporary test configuration."""
    # Setup test environment
    pass

@pytest.fixture
async def new_feature(temp_config):
    """Create feature instance for testing."""
    feature = NewFeature()
    await feature.initialize()
    yield feature
    await feature.shutdown()

class TestNewFeature:
    """Test new feature functionality."""
    
    @pytest.mark.asyncio
    async def test_basic_functionality(self, new_feature):
        """Test basic feature operation."""
        result = await new_feature.process("test input")
        assert result is not None
        assert "expected_key" in result
    
    @pytest.mark.asyncio
    async def test_error_handling(self, new_feature):
        """Test error handling."""
        with pytest.raises(ValueError):
            await new_feature.process(None)
    
    @pytest.mark.asyncio
    async def test_integration(self, new_feature):
        """Test integration with other components."""
        # Integration test code
        pass
```

### 2. Test Categories
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Performance Tests**: Test system performance and resource usage
- **End-to-End Tests**: Test complete user workflows

### 3. Test Data Management
```python
# Use temporary directories for test data
@pytest.fixture
def temp_data_dir():
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir

# Mock external dependencies
@pytest.fixture
def mock_model():
    with patch('localmind.core.model.load_model') as mock:
        mock.return_value = MockModel()
        yield mock
```

## Performance Optimization

### 1. Profiling
```python
# Profile code performance
import cProfile
import pstats

def profile_function():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Code to profile
    result = expensive_function()
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)  # Top 10 functions
    
    return result
```

### 2. Memory Optimization
```python
# Use memory profiling
from memory_profiler import profile

@profile
def memory_intensive_function():
    # Function implementation
    pass

# Monitor memory usage
import tracemalloc

tracemalloc.start()
# Code to monitor
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")
tracemalloc.stop()
```

### 3. Async Optimization
```python
# Use async/await properly
async def optimized_function():
    # Use asyncio.gather for concurrent operations
    results = await asyncio.gather(
        async_operation_1(),
        async_operation_2(),
        async_operation_3()
    )
    return results

# Use connection pooling for database operations
async def with_connection_pool():
    async with database_pool.acquire() as connection:
        result = await connection.execute(query)
        return result
```

## Debugging

### 1. Logging
```python
# Use structured logging
from localmind.utils.logging import get_logger

logger = get_logger(__name__)

def debug_function():
    logger.debug("Debug information", extra={"context": "additional_data"})
    logger.info("Operation completed successfully")
    logger.warning("Potential issue detected")
    logger.error("Error occurred", exc_info=True)
```

### 2. Debug Mode
```python
# Enable debug mode in config.yaml
debug: true
log_level: "DEBUG"

# Use debug utilities
from localmind.utils.debug import debug_print, debug_timer

@debug_timer
async def timed_function():
    # Function implementation
    pass

debug_print("Variable state", {"var1": value1, "var2": value2})
```

### 3. Interactive Debugging
```python
# Use debugger
import pdb; pdb.set_trace()

# Or use ipdb for better interface
import ipdb; ipdb.set_trace()

# Use breakpoint() in Python 3.7+
breakpoint()
```

## Documentation

### 1. Code Documentation
```python
def well_documented_function(param1: str, param2: int = 10) -> dict:
    """
    Brief description of the function.
    
    Longer description explaining the purpose, behavior, and any important
    details about the function.
    
    Args:
        param1: Description of the first parameter.
        param2: Description of the second parameter with default value.
    
    Returns:
        Dictionary containing the result with keys:
        - 'result': The main result
        - 'metadata': Additional information
    
    Raises:
        ValueError: If param1 is empty.
        RuntimeError: If operation fails.
    
    Example:
        >>> result = well_documented_function("test", 20)
        >>> print(result['result'])
        'processed_test'
    """
    if not param1:
        raise ValueError("param1 cannot be empty")
    
    return {
        'result': f"processed_{param1}",
        'metadata': {'param2_used': param2}
    }
```

### 2. API Documentation
```python
# Use type hints and docstrings for API documentation
from typing import Dict, List, Optional
from pydantic import BaseModel

class APIRequest(BaseModel):
    """API request model."""
    message: str
    context: Optional[Dict[str, Any]] = None

async def api_endpoint(request: APIRequest) -> Dict[str, Any]:
    """
    Process API request.
    
    Args:
        request: The API request containing message and optional context.
    
    Returns:
        Response dictionary with processed result.
    """
    pass
```

## Release Process

### 1. Version Management
```bash
# Update version in pyproject.toml
version = "0.2.0"

# Tag release
git tag -a v0.2.0 -m "Release version 0.2.0"
git push origin v0.2.0
```

### 2. Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version number updated
- [ ] Changelog updated
- [ ] Performance benchmarks run
- [ ] Security review completed
- [ ] Backup compatibility verified

### 3. Deployment
```bash
# Create release package
python -m build

# Test installation
pip install dist/localmind-0.2.0-py3-none-any.whl

# Verify installation
python -c "import localmind; print('Installation successful')"
```

## Contributing Guidelines

### 1. Code Style
- Follow PEP 8 style guidelines
- Use type hints for all function parameters and return values
- Write comprehensive docstrings
- Keep functions focused and small
- Use meaningful variable and function names

### 2. Commit Messages
```
feat: add new sentiment analysis capability
fix: resolve memory leak in vector store
docs: update API documentation
test: add integration tests for learning engine
refactor: improve code organization in core module
```

### 3. Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation
4. Run full test suite
5. Submit pull request with clear description
6. Address review feedback
7. Merge after approval

## Troubleshooting

### Common Development Issues

**Import Errors:**
```bash
# Ensure package is installed in development mode
pip install -e .

# Check Python path
python -c "import sys; print(sys.path)"
```

**Test Failures:**
```bash
# Run specific test with verbose output
pytest tests/test_specific.py::TestClass::test_method -v -s

# Run with debugging
pytest tests/test_specific.py --pdb
```

**Performance Issues:**
```bash
# Profile the application
python -m cProfile -o profile.stats run_localmind.py cli
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"
```

**Memory Issues:**
```bash
# Monitor memory usage
python scripts/system_monitor.py --interval 5

# Check for memory leaks
python -m memory_profiler your_script.py
```
