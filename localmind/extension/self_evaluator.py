"""Self-evaluation module for LocalMind AI system."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict

from ..core.config import get_config
from ..storage.database import DatabaseManager
from ..storage.models import LearningEvent, SystemMetrics
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SelfEvaluator:
    """Evaluates system performance and suggests improvements."""
    
    def __init__(self):
        self.config = get_config()
        self.db_manager = DatabaseManager()
        self._initialized = False
        
        # Evaluation metrics
        self.performance_metrics = {
            'response_time': [],
            'accuracy_scores': [],
            'user_satisfaction': [],
            'learning_effectiveness': [],
            'code_success_rate': []
        }
        
        # Evaluation thresholds
        self.thresholds = {
            'response_time_max': 5.0,  # seconds
            'accuracy_min': 0.8,
            'satisfaction_min': 0.7,
            'learning_rate_min': 0.1,
            'code_success_min': 0.6
        }
    
    async def initialize(self) -> None:
        """Initialize the self-evaluator."""
        if self._initialized:
            return
        
        try:
            await self.db_manager.initialize()
            
            # Load historical metrics
            await self._load_historical_metrics()
            
            self._initialized = True
            logger.info("Self-evaluator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize self-evaluator: {e}")
            raise
    
    async def _load_historical_metrics(self) -> None:
        """Load historical performance metrics."""
        try:
            # Load recent system metrics
            async with self.db_manager.get_session() as session:
                # Get metrics from last 7 days
                cutoff_date = datetime.utcnow() - timedelta(days=7)
                
                result = await session.execute(
                    "SELECT metric_name, metric_value FROM system_metrics "
                    "WHERE timestamp > ? ORDER BY timestamp DESC",
                    (cutoff_date,)
                )
                
                metrics = result.fetchall()
                
                for metric_name, metric_value in metrics:
                    if metric_name in self.performance_metrics:
                        self.performance_metrics[metric_name].append(metric_value)
            
            logger.info("Historical metrics loaded")
            
        except Exception as e:
            logger.error(f"Error loading historical metrics: {e}")
    
    async def evaluate_system_performance(self) -> Dict[str, Any]:
        """Evaluate overall system performance."""
        if not self._initialized:
            await self.initialize()
        
        try:
            evaluation = {
                'timestamp': datetime.now().isoformat(),
                'overall_score': 0.0,
                'component_scores': {},
                'recommendations': [],
                'critical_issues': [],
                'performance_trends': {}
            }
            
            # Evaluate different components
            conversation_score = await self._evaluate_conversation_performance()
            learning_score = await self._evaluate_learning_effectiveness()
            code_score = await self._evaluate_code_generation()
            knowledge_score = await self._evaluate_knowledge_base()
            
            evaluation['component_scores'] = {
                'conversation': conversation_score,
                'learning': learning_score,
                'code_generation': code_score,
                'knowledge_base': knowledge_score
            }
            
            # Calculate overall score
            scores = list(evaluation['component_scores'].values())
            evaluation['overall_score'] = sum(scores) / len(scores) if scores else 0.0
            
            # Generate recommendations
            evaluation['recommendations'] = await self._generate_recommendations(evaluation)
            
            # Identify critical issues
            evaluation['critical_issues'] = await self._identify_critical_issues(evaluation)
            
            # Analyze performance trends
            evaluation['performance_trends'] = await self._analyze_performance_trends()
            
            # Store evaluation results
            await self._store_evaluation_results(evaluation)
            
            logger.info(f"System evaluation completed. Overall score: {evaluation['overall_score']:.2f}")
            return evaluation
            
        except Exception as e:
            logger.error(f"Error evaluating system performance: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'overall_score': 0.0
            }
    
    async def _evaluate_conversation_performance(self) -> float:
        """Evaluate conversation system performance."""
        try:
            async with self.db_manager.get_session() as session:
                # Get recent conversation metrics
                cutoff_date = datetime.utcnow() - timedelta(days=1)
                
                result = await session.execute(
                    "SELECT COUNT(*) as total_conversations FROM conversations "
                    "WHERE timestamp > ?",
                    (cutoff_date,)
                )
                total_conversations = result.fetchone()[0]
                
                # Calculate response time metrics
                avg_response_time = sum(self.performance_metrics['response_time'][-100:]) / max(len(self.performance_metrics['response_time'][-100:]), 1)
                
                # Score based on response time and conversation volume
                time_score = max(0, 1 - (avg_response_time / self.thresholds['response_time_max']))
                volume_score = min(1.0, total_conversations / 10)  # Normalize to 10 conversations per day
                
                return (time_score + volume_score) / 2
                
        except Exception as e:
            logger.error(f"Error evaluating conversation performance: {e}")
            return 0.5
    
    async def _evaluate_learning_effectiveness(self) -> float:
        """Evaluate learning system effectiveness."""
        try:
            async with self.db_manager.get_session() as session:
                # Get recent learning events
                cutoff_date = datetime.utcnow() - timedelta(days=7)
                
                result = await session.execute(
                    "SELECT success, COUNT(*) as count FROM learning_events "
                    "WHERE timestamp > ? GROUP BY success",
                    (cutoff_date,)
                )
                
                learning_stats = dict(result.fetchall())
                total_events = sum(learning_stats.values())
                successful_events = learning_stats.get(True, 0)
                
                if total_events == 0:
                    return 0.5
                
                success_rate = successful_events / total_events
                
                # Score based on success rate and learning volume
                rate_score = success_rate
                volume_score = min(1.0, total_events / 50)  # Normalize to 50 learning events per week
                
                return (rate_score + volume_score) / 2
                
        except Exception as e:
            logger.error(f"Error evaluating learning effectiveness: {e}")
            return 0.5
    
    async def _evaluate_code_generation(self) -> float:
        """Evaluate code generation performance."""
        try:
            # Get code success rates from metrics
            if self.performance_metrics['code_success_rate']:
                avg_success_rate = sum(self.performance_metrics['code_success_rate'][-50:]) / len(self.performance_metrics['code_success_rate'][-50:])
                return min(1.0, avg_success_rate / self.thresholds['code_success_min'])
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Error evaluating code generation: {e}")
            return 0.5
    
    async def _evaluate_knowledge_base(self) -> float:
        """Evaluate knowledge base quality and usage."""
        try:
            async with self.db_manager.get_session() as session:
                # Count knowledge entries
                result = await session.execute("SELECT COUNT(*) FROM knowledge")
                knowledge_count = result.fetchone()[0]
                
                # Count recent access
                cutoff_date = datetime.utcnow() - timedelta(days=7)
                result = await session.execute(
                    "SELECT SUM(access_count) FROM knowledge WHERE last_accessed > ?",
                    (cutoff_date,)
                )
                recent_access = result.fetchone()[0] or 0
                
                # Score based on knowledge volume and usage
                volume_score = min(1.0, knowledge_count / 100)  # Normalize to 100 knowledge entries
                usage_score = min(1.0, recent_access / 50)  # Normalize to 50 accesses per week
                
                return (volume_score + usage_score) / 2
                
        except Exception as e:
            logger.error(f"Error evaluating knowledge base: {e}")
            return 0.5
    
    async def _generate_recommendations(self, evaluation: Dict[str, Any]) -> List[str]:
        """Generate improvement recommendations based on evaluation."""
        recommendations = []
        
        try:
            scores = evaluation['component_scores']
            
            # Conversation recommendations
            if scores.get('conversation', 0) < 0.7:
                recommendations.append("Improve conversation response time and quality")
                recommendations.append("Consider optimizing the language model or using a faster model")
            
            # Learning recommendations
            if scores.get('learning', 0) < 0.7:
                recommendations.append("Enhance learning algorithms and pattern detection")
                recommendations.append("Increase learning data collection and processing")
            
            # Code generation recommendations
            if scores.get('code_generation', 0) < 0.7:
                recommendations.append("Improve code generation templates and validation")
                recommendations.append("Add more programming language support")
            
            # Knowledge base recommendations
            if scores.get('knowledge_base', 0) < 0.7:
                recommendations.append("Expand knowledge base with more diverse content")
                recommendations.append("Improve knowledge search and retrieval algorithms")
            
            # Overall recommendations
            if evaluation['overall_score'] < 0.6:
                recommendations.append("Consider system-wide optimization and resource allocation")
                recommendations.append("Review and update core algorithms")
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations.append("Unable to generate specific recommendations due to evaluation error")
        
        return recommendations
    
    async def _identify_critical_issues(self, evaluation: Dict[str, Any]) -> List[str]:
        """Identify critical issues that need immediate attention."""
        critical_issues = []
        
        try:
            scores = evaluation['component_scores']
            
            # Check for critical performance issues
            if evaluation['overall_score'] < 0.3:
                critical_issues.append("CRITICAL: Overall system performance is severely degraded")
            
            for component, score in scores.items():
                if score < 0.3:
                    critical_issues.append(f"CRITICAL: {component} system is failing (score: {score:.2f})")
            
            # Check response time
            if self.performance_metrics['response_time']:
                avg_response_time = sum(self.performance_metrics['response_time'][-10:]) / len(self.performance_metrics['response_time'][-10:])
                if avg_response_time > self.thresholds['response_time_max'] * 2:
                    critical_issues.append(f"CRITICAL: Response time is too high ({avg_response_time:.2f}s)")
            
        except Exception as e:
            logger.error(f"Error identifying critical issues: {e}")
            critical_issues.append("Unable to identify critical issues due to evaluation error")
        
        return critical_issues
    
    async def _analyze_performance_trends(self) -> Dict[str, Any]:
        """Analyze performance trends over time."""
        trends = {}
        
        try:
            for metric_name, values in self.performance_metrics.items():
                if len(values) >= 2:
                    recent_avg = sum(values[-10:]) / len(values[-10:])
                    older_avg = sum(values[-20:-10]) / max(len(values[-20:-10]), 1)
                    
                    if older_avg > 0:
                        trend_direction = "improving" if recent_avg > older_avg else "declining"
                        trend_magnitude = abs(recent_avg - older_avg) / older_avg
                    else:
                        trend_direction = "stable"
                        trend_magnitude = 0
                    
                    trends[metric_name] = {
                        'direction': trend_direction,
                        'magnitude': trend_magnitude,
                        'recent_average': recent_avg,
                        'previous_average': older_avg
                    }
        
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {e}")
        
        return trends
    
    async def _store_evaluation_results(self, evaluation: Dict[str, Any]) -> None:
        """Store evaluation results for future analysis."""
        try:
            async with self.db_manager.get_session() as session:
                # Store overall score
                metric = SystemMetrics(
                    metric_name='overall_performance_score',
                    metric_value=evaluation['overall_score'],
                    metric_type='gauge',
                    metadata=evaluation
                )
                session.add(metric)
                
                # Store component scores
                for component, score in evaluation['component_scores'].items():
                    metric = SystemMetrics(
                        metric_name=f'{component}_performance_score',
                        metric_value=score,
                        metric_type='gauge'
                    )
                    session.add(metric)
                
                await session.commit()
            
        except Exception as e:
            logger.error(f"Error storing evaluation results: {e}")
    
    async def record_metric(self, metric_name: str, value: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record a performance metric."""
        try:
            if metric_name in self.performance_metrics:
                self.performance_metrics[metric_name].append(value)
                
                # Keep only recent values
                if len(self.performance_metrics[metric_name]) > 1000:
                    self.performance_metrics[metric_name] = self.performance_metrics[metric_name][-500:]
            
            # Store in database
            async with self.db_manager.get_session() as session:
                metric = SystemMetrics(
                    metric_name=metric_name,
                    metric_value=value,
                    metric_type='gauge',
                    metadata=metadata
                )
                session.add(metric)
                await session.commit()
            
        except Exception as e:
            logger.error(f"Error recording metric {metric_name}: {e}")
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of current performance metrics."""
        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'metrics': {}
            }
            
            for metric_name, values in self.performance_metrics.items():
                if values:
                    summary['metrics'][metric_name] = {
                        'current': values[-1],
                        'average': sum(values) / len(values),
                        'min': min(values),
                        'max': max(values),
                        'count': len(values)
                    }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {'error': str(e)}
    
    async def shutdown(self) -> None:
        """Shutdown the self-evaluator."""
        logger.info("Self-evaluator shutting down...")
