"""Plugin engine for extending LocalMind AI capabilities."""

import importlib
import inspect
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable, Type
from datetime import datetime
import asyncio

from ..core.config import get_config
from ..utils.logging import get_logger
from ..utils.helpers import ensure_directory, load_json, save_json

logger = get_logger(__name__)


class Plugin:
    """Base class for LocalMind plugins."""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.enabled = True
        self.metadata = {}
    
    async def initialize(self) -> None:
        """Initialize the plugin."""
        pass
    
    async def shutdown(self) -> None:
        """Shutdown the plugin."""
        pass
    
    def get_capabilities(self) -> List[str]:
        """Return list of capabilities this plugin provides."""
        return []
    
    def get_metadata(self) -> Dict[str, Any]:
        """Return plugin metadata."""
        return {
            'name': self.name,
            'version': self.version,
            'enabled': self.enabled,
            'capabilities': self.get_capabilities(),
            **self.metadata
        }


class PluginEngine:
    """Manages plugins and extensions for LocalMind AI."""
    
    def __init__(self):
        self.config = get_config()
        self.plugins: Dict[str, Plugin] = {}
        self.plugin_hooks: Dict[str, List[Callable]] = {}
        self.plugin_directory = Path(self.config.data_dir) / "plugins"
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the plugin engine."""
        if self._initialized:
            return
        
        try:
            # Ensure plugin directory exists
            ensure_directory(str(self.plugin_directory))
            
            # Load built-in plugins
            await self._load_builtin_plugins()
            
            # Load external plugins
            await self._load_external_plugins()
            
            self._initialized = True
            logger.info(f"Plugin engine initialized with {len(self.plugins)} plugins")
            
        except Exception as e:
            logger.error(f"Failed to initialize plugin engine: {e}")
            raise
    
    async def _load_builtin_plugins(self) -> None:
        """Load built-in plugins."""
        try:
            # Create built-in plugins
            builtin_plugins = [
                CodeAnalysisPlugin(),
                KnowledgeEnhancementPlugin(),
                ConversationAnalyticsPlugin(),
                SystemMonitoringPlugin()
            ]
            
            for plugin in builtin_plugins:
                await self.register_plugin(plugin)
                
        except Exception as e:
            logger.error(f"Error loading built-in plugins: {e}")
    
    async def _load_external_plugins(self) -> None:
        """Load external plugins from the plugin directory."""
        try:
            plugin_files = list(self.plugin_directory.glob("*.py"))
            
            for plugin_file in plugin_files:
                if plugin_file.name.startswith("__"):
                    continue
                
                try:
                    await self._load_plugin_from_file(plugin_file)
                except Exception as e:
                    logger.error(f"Error loading plugin {plugin_file}: {e}")
                    
        except Exception as e:
            logger.error(f"Error loading external plugins: {e}")
    
    async def _load_plugin_from_file(self, plugin_file: Path) -> None:
        """Load a plugin from a Python file."""
        try:
            # Import the plugin module
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                plugin_file.stem, plugin_file
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find plugin classes
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, Plugin) and 
                    obj != Plugin):
                    
                    # Instantiate and register plugin
                    plugin = obj()
                    await self.register_plugin(plugin)
                    logger.info(f"Loaded external plugin: {plugin.name}")
                    
        except Exception as e:
            logger.error(f"Error loading plugin from {plugin_file}: {e}")
    
    async def register_plugin(self, plugin: Plugin) -> bool:
        """Register a plugin with the engine."""
        try:
            if plugin.name in self.plugins:
                logger.warning(f"Plugin {plugin.name} already registered")
                return False
            
            # Initialize the plugin
            await plugin.initialize()
            
            # Register the plugin
            self.plugins[plugin.name] = plugin
            
            # Register plugin hooks
            await self._register_plugin_hooks(plugin)
            
            logger.info(f"Registered plugin: {plugin.name} v{plugin.version}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering plugin {plugin.name}: {e}")
            return False
    
    async def _register_plugin_hooks(self, plugin: Plugin) -> None:
        """Register hooks for a plugin."""
        try:
            # Look for hook methods in the plugin
            for method_name in dir(plugin):
                if method_name.startswith('hook_'):
                    hook_name = method_name[5:]  # Remove 'hook_' prefix
                    hook_method = getattr(plugin, method_name)
                    
                    if callable(hook_method):
                        if hook_name not in self.plugin_hooks:
                            self.plugin_hooks[hook_name] = []
                        self.plugin_hooks[hook_name].append(hook_method)
                        
        except Exception as e:
            logger.error(f"Error registering hooks for plugin {plugin.name}: {e}")
    
    async def unregister_plugin(self, plugin_name: str) -> bool:
        """Unregister a plugin."""
        try:
            if plugin_name not in self.plugins:
                return False
            
            plugin = self.plugins[plugin_name]
            
            # Shutdown the plugin
            await plugin.shutdown()
            
            # Remove from plugins
            del self.plugins[plugin_name]
            
            # Remove hooks
            for hook_name, hooks in self.plugin_hooks.items():
                self.plugin_hooks[hook_name] = [
                    hook for hook in hooks 
                    if not hasattr(hook, '__self__') or hook.__self__ != plugin
                ]
            
            logger.info(f"Unregistered plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering plugin {plugin_name}: {e}")
            return False
    
    async def call_hook(self, hook_name: str, *args, **kwargs) -> List[Any]:
        """Call all registered hooks for a given event."""
        results = []
        
        if hook_name in self.plugin_hooks:
            for hook in self.plugin_hooks[hook_name]:
                try:
                    if asyncio.iscoroutinefunction(hook):
                        result = await hook(*args, **kwargs)
                    else:
                        result = hook(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error calling hook {hook_name}: {e}")
        
        return results
    
    def get_plugin(self, plugin_name: str) -> Optional[Plugin]:
        """Get a plugin by name."""
        return self.plugins.get(plugin_name)
    
    def list_plugins(self) -> List[Dict[str, Any]]:
        """List all registered plugins."""
        return [plugin.get_metadata() for plugin in self.plugins.values()]
    
    def get_capabilities(self) -> List[str]:
        """Get all capabilities provided by plugins."""
        capabilities = []
        for plugin in self.plugins.values():
            if plugin.enabled:
                capabilities.extend(plugin.get_capabilities())
        return list(set(capabilities))
    
    async def enable_plugin(self, plugin_name: str) -> bool:
        """Enable a plugin."""
        if plugin_name in self.plugins:
            self.plugins[plugin_name].enabled = True
            logger.info(f"Enabled plugin: {plugin_name}")
            return True
        return False
    
    async def disable_plugin(self, plugin_name: str) -> bool:
        """Disable a plugin."""
        if plugin_name in self.plugins:
            self.plugins[plugin_name].enabled = False
            logger.info(f"Disabled plugin: {plugin_name}")
            return True
        return False
    
    async def create_plugin_template(self, plugin_name: str) -> str:
        """Create a template for a new plugin."""
        template = f'''"""
{plugin_name} plugin for LocalMind AI system.
"""

from localmind.extension.plugin_engine import Plugin
from localmind.utils.logging import get_logger

logger = get_logger(__name__)


class {plugin_name.title()}Plugin(Plugin):
    """Custom plugin for {plugin_name} functionality."""
    
    def __init__(self):
        super().__init__(name="{plugin_name}", version="1.0.0")
        self.metadata = {{
            "description": "Custom plugin for {plugin_name}",
            "author": "LocalMind User",
            "created": "{datetime.now().isoformat()}"
        }}
    
    async def initialize(self) -> None:
        """Initialize the plugin."""
        logger.info(f"Initializing {{self.name}} plugin")
        # Add initialization code here
    
    async def shutdown(self) -> None:
        """Shutdown the plugin."""
        logger.info(f"Shutting down {{self.name}} plugin")
        # Add cleanup code here
    
    def get_capabilities(self) -> list[str]:
        """Return plugin capabilities."""
        return ["{plugin_name}_capability"]
    
    # Hook examples - uncomment and modify as needed
    
    # async def hook_before_chat(self, message: str, context: dict) -> dict:
    #     """Called before processing a chat message."""
    #     return {{"modified_message": message, "additional_context": {{}}}}
    
    # async def hook_after_chat(self, message: str, response: str, context: dict) -> dict:
    #     """Called after generating a chat response."""
    #     return {{"analysis": "response_analysis"}}
    
    # async def hook_code_generated(self, description: str, code: str, language: str) -> dict:
    #     """Called after code generation."""
    #     return {{"code_quality_score": 0.8}}
'''
        
        plugin_file = self.plugin_directory / f"{plugin_name}.py"
        with open(plugin_file, 'w') as f:
            f.write(template)
        
        logger.info(f"Created plugin template: {plugin_file}")
        return str(plugin_file)
    
    async def shutdown(self) -> None:
        """Shutdown all plugins."""
        for plugin in self.plugins.values():
            try:
                await plugin.shutdown()
            except Exception as e:
                logger.error(f"Error shutting down plugin {plugin.name}: {e}")
        
        logger.info("Plugin engine shutdown complete")


# Built-in plugins

class CodeAnalysisPlugin(Plugin):
    """Plugin for analyzing generated code quality."""
    
    def __init__(self):
        super().__init__("code_analysis", "1.0.0")
        self.metadata = {
            "description": "Analyzes code quality and provides suggestions",
            "author": "LocalMind System"
        }
    
    def get_capabilities(self) -> List[str]:
        return ["code_quality_analysis", "code_suggestions"]
    
    async def hook_code_generated(self, description: str, code: str, language: str) -> Dict[str, Any]:
        """Analyze generated code quality."""
        try:
            analysis = {
                "lines_of_code": len(code.split('\n')),
                "has_comments": '#' in code or '//' in code,
                "complexity_score": min(len(code) / 100, 1.0),
                "suggestions": []
            }
            
            # Add basic suggestions
            if not analysis["has_comments"]:
                analysis["suggestions"].append("Consider adding comments to explain the code")
            
            if analysis["lines_of_code"] > 50:
                analysis["suggestions"].append("Consider breaking this into smaller functions")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in code analysis: {e}")
            return {}


class KnowledgeEnhancementPlugin(Plugin):
    """Plugin for enhancing knowledge base entries."""
    
    def __init__(self):
        super().__init__("knowledge_enhancement", "1.0.0")
        self.metadata = {
            "description": "Enhances knowledge base with additional context",
            "author": "LocalMind System"
        }
    
    def get_capabilities(self) -> List[str]:
        return ["knowledge_enhancement", "context_enrichment"]
    
    async def hook_knowledge_added(self, title: str, content: str, category: str) -> Dict[str, Any]:
        """Enhance knowledge entries with additional context."""
        try:
            enhancement = {
                "word_count": len(content.split()),
                "estimated_reading_time": len(content.split()) // 200,  # ~200 words per minute
                "topics": self._extract_topics(content),
                "complexity": "basic" if len(content) < 500 else "intermediate" if len(content) < 1500 else "advanced"
            }
            
            return enhancement
            
        except Exception as e:
            logger.error(f"Error in knowledge enhancement: {e}")
            return {}
    
    def _extract_topics(self, content: str) -> List[str]:
        """Extract topics from content."""
        # Simple topic extraction
        words = content.lower().split()
        common_topics = ['python', 'javascript', 'programming', 'algorithm', 'data', 'function', 'class']
        return [topic for topic in common_topics if topic in words]


class ConversationAnalyticsPlugin(Plugin):
    """Plugin for analyzing conversation patterns."""
    
    def __init__(self):
        super().__init__("conversation_analytics", "1.0.0")
        self.metadata = {
            "description": "Analyzes conversation patterns and user preferences",
            "author": "LocalMind System"
        }
    
    def get_capabilities(self) -> List[str]:
        return ["conversation_analysis", "user_preference_tracking"]
    
    async def hook_after_chat(self, message: str, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze conversation patterns."""
        try:
            analysis = {
                "message_length": len(message),
                "response_length": len(response),
                "question_type": self._classify_question(message),
                "sentiment": self._analyze_sentiment(message),
                "topics": self._extract_conversation_topics(message)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in conversation analysis: {e}")
            return {}
    
    def _classify_question(self, message: str) -> str:
        """Classify the type of question."""
        message_lower = message.lower()
        if any(word in message_lower for word in ['what', 'define', 'explain']):
            return 'information'
        elif any(word in message_lower for word in ['how', 'create', 'make']):
            return 'instruction'
        elif any(word in message_lower for word in ['why', 'because']):
            return 'reasoning'
        else:
            return 'general'
    
    def _analyze_sentiment(self, message: str) -> str:
        """Basic sentiment analysis."""
        positive_words = ['good', 'great', 'excellent', 'thanks', 'please']
        negative_words = ['bad', 'error', 'problem', 'issue', 'wrong']
        
        message_lower = message.lower()
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _extract_conversation_topics(self, message: str) -> List[str]:
        """Extract topics from conversation."""
        tech_topics = ['python', 'javascript', 'programming', 'code', 'algorithm', 'data', 'ai', 'machine learning']
        message_lower = message.lower()
        return [topic for topic in tech_topics if topic in message_lower]


class SystemMonitoringPlugin(Plugin):
    """Plugin for monitoring system performance."""
    
    def __init__(self):
        super().__init__("system_monitoring", "1.0.0")
        self.metadata = {
            "description": "Monitors system performance and resource usage",
            "author": "LocalMind System"
        }
    
    def get_capabilities(self) -> List[str]:
        return ["performance_monitoring", "resource_tracking"]
    
    async def hook_system_stats(self) -> Dict[str, Any]:
        """Monitor system performance."""
        try:
            import psutil
            
            stats = {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "timestamp": datetime.now().isoformat()
            }
            
            return stats
            
        except ImportError:
            return {"error": "psutil not available for system monitoring"}
        except Exception as e:
            logger.error(f"Error in system monitoring: {e}")
            return {"error": str(e)}
