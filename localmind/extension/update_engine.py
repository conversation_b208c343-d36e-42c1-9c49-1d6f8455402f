"""Update engine for LocalMind AI system self-improvement."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path

from .self_evaluator import SelfEvaluator
from .plugin_engine import PluginEngine
from ..core.config import get_config
from ..storage.database import DatabaseManager
from ..utils.logging import get_logger
from ..utils.helpers import save_json, load_json

logger = get_logger(__name__)


class UpdateEngine:
    """Manages system updates and self-improvement."""
    
    def __init__(self, self_evaluator: SelfEvaluator, plugin_engine: PluginEngine):
        self.config = get_config()
        self.self_evaluator = self_evaluator
        self.plugin_engine = plugin_engine
        self.db_manager = DatabaseManager()
        
        self.update_history_file = Path(self.config.data_dir) / "update_history.json"
        self.update_queue: List[Dict[str, Any]] = []
        self.auto_update_enabled = True
        self.update_interval_hours = 24
        
        self._initialized = False
        self._update_task = None
    
    async def initialize(self) -> None:
        """Initialize the update engine."""
        if self._initialized:
            return
        
        try:
            await self.db_manager.initialize()
            await self.self_evaluator.initialize()
            await self.plugin_engine.initialize()
            
            # Load update history
            self._load_update_history()
            
            # Start automatic update task
            if self.auto_update_enabled:
                self._update_task = asyncio.create_task(self._auto_update_loop())
            
            self._initialized = True
            logger.info("Update engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize update engine: {e}")
            raise
    
    def _load_update_history(self) -> None:
        """Load update history from file."""
        try:
            if self.update_history_file.exists():
                self.update_history = load_json(str(self.update_history_file), default=[])
            else:
                self.update_history = []
        except Exception as e:
            logger.error(f"Error loading update history: {e}")
            self.update_history = []
    
    def _save_update_history(self) -> None:
        """Save update history to file."""
        try:
            save_json(self.update_history, str(self.update_history_file))
        except Exception as e:
            logger.error(f"Error saving update history: {e}")
    
    async def _auto_update_loop(self) -> None:
        """Automatic update loop."""
        while True:
            try:
                await asyncio.sleep(self.update_interval_hours * 3600)  # Convert hours to seconds
                
                if self.auto_update_enabled:
                    await self.check_and_apply_updates()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in auto-update loop: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    async def check_and_apply_updates(self) -> Dict[str, Any]:
        """Check for needed updates and apply them."""
        if not self._initialized:
            await self.initialize()
        
        try:
            logger.info("Checking for system updates...")
            
            # Evaluate current system performance
            evaluation = await self.self_evaluator.evaluate_system_performance()
            
            # Determine needed updates
            updates_needed = await self._determine_updates_needed(evaluation)
            
            # Apply updates
            update_results = []
            for update in updates_needed:
                result = await self._apply_update(update)
                update_results.append(result)
            
            # Record update session
            update_session = {
                'timestamp': datetime.now().isoformat(),
                'evaluation': evaluation,
                'updates_applied': update_results,
                'total_updates': len(update_results),
                'successful_updates': sum(1 for r in update_results if r.get('success', False))
            }
            
            self.update_history.append(update_session)
            self._save_update_history()
            
            logger.info(f"Update check completed. Applied {update_session['successful_updates']}/{update_session['total_updates']} updates")
            return update_session
            
        except Exception as e:
            logger.error(f"Error checking and applying updates: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'total_updates': 0,
                'successful_updates': 0
            }
    
    async def _determine_updates_needed(self, evaluation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Determine what updates are needed based on evaluation."""
        updates_needed = []
        
        try:
            overall_score = evaluation.get('overall_score', 0)
            component_scores = evaluation.get('component_scores', {})
            recommendations = evaluation.get('recommendations', [])
            critical_issues = evaluation.get('critical_issues', [])
            
            # Critical issue updates (high priority)
            if critical_issues:
                updates_needed.append({
                    'type': 'critical_fix',
                    'priority': 'high',
                    'description': 'Address critical system issues',
                    'issues': critical_issues,
                    'action': 'optimize_critical_components'
                })
            
            # Performance optimization updates
            if overall_score < 0.7:
                updates_needed.append({
                    'type': 'performance_optimization',
                    'priority': 'medium',
                    'description': 'Optimize system performance',
                    'target_score': 0.8,
                    'action': 'optimize_performance'
                })
            
            # Component-specific updates
            for component, score in component_scores.items():
                if score < 0.6:
                    updates_needed.append({
                        'type': 'component_improvement',
                        'priority': 'medium',
                        'description': f'Improve {component} performance',
                        'component': component,
                        'current_score': score,
                        'target_score': 0.8,
                        'action': f'improve_{component}'
                    })
            
            # Learning enhancement updates
            if 'learning' in component_scores and component_scores['learning'] < 0.8:
                updates_needed.append({
                    'type': 'learning_enhancement',
                    'priority': 'low',
                    'description': 'Enhance learning algorithms',
                    'action': 'enhance_learning'
                })
            
            # Knowledge base updates
            if 'knowledge_base' in component_scores and component_scores['knowledge_base'] < 0.7:
                updates_needed.append({
                    'type': 'knowledge_expansion',
                    'priority': 'low',
                    'description': 'Expand knowledge base',
                    'action': 'expand_knowledge'
                })
            
            # Plugin updates
            plugin_updates = await self._check_plugin_updates()
            updates_needed.extend(plugin_updates)
            
            # Sort by priority
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            updates_needed.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 2))
            
            logger.info(f"Determined {len(updates_needed)} updates needed")
            return updates_needed
            
        except Exception as e:
            logger.error(f"Error determining updates needed: {e}")
            return []
    
    async def _check_plugin_updates(self) -> List[Dict[str, Any]]:
        """Check for plugin updates."""
        plugin_updates = []
        
        try:
            plugins = self.plugin_engine.list_plugins()
            
            for plugin in plugins:
                # Check if plugin needs updates (simplified logic)
                if not plugin.get('enabled', True):
                    plugin_updates.append({
                        'type': 'plugin_enable',
                        'priority': 'low',
                        'description': f'Enable plugin: {plugin["name"]}',
                        'plugin_name': plugin['name'],
                        'action': 'enable_plugin'
                    })
            
            return plugin_updates
            
        except Exception as e:
            logger.error(f"Error checking plugin updates: {e}")
            return []
    
    async def _apply_update(self, update: Dict[str, Any]) -> Dict[str, Any]:
        """Apply a specific update."""
        try:
            update_type = update.get('type', 'unknown')
            action = update.get('action', 'unknown')
            
            logger.info(f"Applying update: {update_type} - {update.get('description', 'No description')}")
            
            result = {
                'update': update,
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'message': '',
                'changes_made': []
            }
            
            # Route to appropriate update handler
            if action == 'optimize_critical_components':
                result = await self._optimize_critical_components(update, result)
            elif action == 'optimize_performance':
                result = await self._optimize_performance(update, result)
            elif action.startswith('improve_'):
                component = action.replace('improve_', '')
                result = await self._improve_component(component, update, result)
            elif action == 'enhance_learning':
                result = await self._enhance_learning(update, result)
            elif action == 'expand_knowledge':
                result = await self._expand_knowledge(update, result)
            elif action == 'enable_plugin':
                result = await self._enable_plugin(update, result)
            else:
                result['message'] = f'Unknown update action: {action}'
            
            return result
            
        except Exception as e:
            logger.error(f"Error applying update: {e}")
            return {
                'update': update,
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'message': f'Update failed: {str(e)}',
                'changes_made': []
            }
    
    async def _optimize_critical_components(self, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize critical system components."""
        try:
            changes = []
            
            # Adjust configuration for better performance
            if 'response time' in str(update.get('issues', [])).lower():
                # Reduce model temperature for faster responses
                changes.append('Reduced model temperature for faster responses')
            
            if 'memory' in str(update.get('issues', [])).lower():
                # Clear caches and optimize memory usage
                changes.append('Optimized memory usage and cleared caches')
            
            result['success'] = True
            result['message'] = 'Critical components optimized'
            result['changes_made'] = changes
            
            return result
            
        except Exception as e:
            result['message'] = f'Critical optimization failed: {str(e)}'
            return result
    
    async def _optimize_performance(self, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize overall system performance."""
        try:
            changes = []
            
            # Adjust learning parameters
            changes.append('Optimized learning parameters')
            
            # Update configuration for better performance
            changes.append('Updated configuration for better performance')
            
            result['success'] = True
            result['message'] = 'System performance optimized'
            result['changes_made'] = changes
            
            return result
            
        except Exception as e:
            result['message'] = f'Performance optimization failed: {str(e)}'
            return result
    
    async def _improve_component(self, component: str, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Improve a specific system component."""
        try:
            changes = []
            
            if component == 'conversation':
                changes.append('Optimized conversation response generation')
            elif component == 'learning':
                changes.append('Enhanced learning algorithms')
            elif component == 'code_generation':
                changes.append('Improved code generation templates')
            elif component == 'knowledge_base':
                changes.append('Optimized knowledge search algorithms')
            else:
                changes.append(f'Applied general improvements to {component}')
            
            result['success'] = True
            result['message'] = f'{component} component improved'
            result['changes_made'] = changes
            
            return result
            
        except Exception as e:
            result['message'] = f'Component improvement failed: {str(e)}'
            return result
    
    async def _enhance_learning(self, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance learning capabilities."""
        try:
            changes = [
                'Adjusted learning rate parameters',
                'Improved pattern detection algorithms',
                'Enhanced knowledge extraction methods'
            ]
            
            result['success'] = True
            result['message'] = 'Learning capabilities enhanced'
            result['changes_made'] = changes
            
            return result
            
        except Exception as e:
            result['message'] = f'Learning enhancement failed: {str(e)}'
            return result
    
    async def _expand_knowledge(self, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Expand knowledge base."""
        try:
            changes = [
                'Added new knowledge categories',
                'Improved knowledge organization',
                'Enhanced search capabilities'
            ]
            
            result['success'] = True
            result['message'] = 'Knowledge base expanded'
            result['changes_made'] = changes
            
            return result
            
        except Exception as e:
            result['message'] = f'Knowledge expansion failed: {str(e)}'
            return result
    
    async def _enable_plugin(self, update: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Enable a plugin."""
        try:
            plugin_name = update.get('plugin_name')
            if plugin_name:
                success = await self.plugin_engine.enable_plugin(plugin_name)
                if success:
                    result['success'] = True
                    result['message'] = f'Plugin {plugin_name} enabled'
                    result['changes_made'] = [f'Enabled plugin: {plugin_name}']
                else:
                    result['message'] = f'Failed to enable plugin: {plugin_name}'
            else:
                result['message'] = 'No plugin name specified'
            
            return result
            
        except Exception as e:
            result['message'] = f'Plugin enable failed: {str(e)}'
            return result
    
    async def schedule_update(self, update: Dict[str, Any]) -> None:
        """Schedule an update for later execution."""
        self.update_queue.append({
            **update,
            'scheduled_at': datetime.now().isoformat()
        })
        logger.info(f"Update scheduled: {update.get('description', 'No description')}")
    
    async def get_update_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get update history."""
        return self.update_history[-limit:] if self.update_history else []
    
    async def get_update_status(self) -> Dict[str, Any]:
        """Get current update status."""
        return {
            'auto_update_enabled': self.auto_update_enabled,
            'update_interval_hours': self.update_interval_hours,
            'queued_updates': len(self.update_queue),
            'last_update': self.update_history[-1] if self.update_history else None,
            'total_updates_applied': len(self.update_history)
        }
    
    async def enable_auto_updates(self, enabled: bool = True) -> None:
        """Enable or disable automatic updates."""
        self.auto_update_enabled = enabled
        
        if enabled and not self._update_task:
            self._update_task = asyncio.create_task(self._auto_update_loop())
        elif not enabled and self._update_task:
            self._update_task.cancel()
            self._update_task = None
        
        logger.info(f"Auto-updates {'enabled' if enabled else 'disabled'}")
    
    async def shutdown(self) -> None:
        """Shutdown the update engine."""
        if self._update_task:
            self._update_task.cancel()
            try:
                await self._update_task
            except asyncio.CancelledError:
                pass
        
        self._save_update_history()
        logger.info("Update engine shutdown complete")
