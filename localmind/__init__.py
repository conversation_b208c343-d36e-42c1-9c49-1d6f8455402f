"""
LocalMind AI System

A local AI system with continuous learning, knowledge base development,
conversational interface, code generation, and self-extension capabilities.
"""

__version__ = "0.1.0"
__author__ = "LocalMind Developer"
__email__ = "<EMAIL>"

from .core.ai_engine import LocalMindEngine
from .interfaces.api import LocalMindAPI
from .core.config import Config

__all__ = [
    "LocalMindEngine",
    "LocalMindAPI", 
    "Config",
]
