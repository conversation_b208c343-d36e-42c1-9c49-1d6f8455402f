"""Web interface for LocalMind AI system."""

import asyncio
import sys
from typing import Dict, Any, Optional

import streamlit as st
from streamlit.runtime.scriptrunner import add_script_run_ctx

from .interfaces.api import LocalMindAPI
from .utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Initialize API
@st.cache_resource
def get_api():
    """Get cached API instance."""
    return LocalMindAPI()

def main():
    """Main web interface."""
    st.set_page_config(
        page_title="LocalMind AI",
        page_icon="🧠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize API
    api = get_api()
    
    # Sidebar
    with st.sidebar:
        st.title("🧠 LocalMind AI")
        st.markdown("---")
        
        # Navigation
        page = st.selectbox(
            "Navigate to:",
            ["Chat", "Code Generation", "System Stats", "Settings"]
        )
        
        st.markdown("---")
        
        # Session management
        st.subheader("Session Management")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Save Session"):
                try:
                    session_name = asyncio.run(api.save_session())
                    st.success(f"Session saved: {session_name}")
                except Exception as e:
                    st.error(f"Error saving session: {e}")
        
        with col2:
            if st.button("Clear History"):
                try:
                    asyncio.run(api.clear_conversation_history())
                    st.success("History cleared")
                    st.rerun()
                except Exception as e:
                    st.error(f"Error clearing history: {e}")
    
    # Main content
    if page == "Chat":
        chat_page(api)
    elif page == "Code Generation":
        code_page(api)
    elif page == "System Stats":
        stats_page(api)
    elif page == "Settings":
        settings_page(api)

def chat_page(api: LocalMindAPI):
    """Chat interface page."""
    st.title("💬 Chat with LocalMind")
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me anything..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                try:
                    response = asyncio.run(api.chat(prompt))
                    st.markdown(response)
                    
                    # Add assistant response to chat history
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    
                except Exception as e:
                    error_msg = f"Error: {str(e)}"
                    st.error(error_msg)
                    st.session_state.messages.append({"role": "assistant", "content": error_msg})

def code_page(api: LocalMindAPI):
    """Code generation interface page."""
    st.title("💻 Code Generation")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Code Description")
        description = st.text_area(
            "Describe what you want the code to do:",
            height=100,
            placeholder="e.g., Create a function that sorts a list of numbers"
        )
    
    with col2:
        st.subheader("Settings")
        language = st.selectbox(
            "Programming Language:",
            ["python", "javascript", "bash"]
        )
        
        execute_code = st.checkbox("Execute code after generation", value=False)
    
    if st.button("Generate Code", type="primary"):
        if description:
            with st.spinner("Generating code..."):
                try:
                    result = asyncio.run(api.generate_code(description, language))
                    
                    if result.get('success', False):
                        st.subheader("Generated Code")
                        st.code(result['code'], language=language)
                        
                        if result.get('explanation'):
                            st.subheader("Explanation")
                            st.markdown(result['explanation'])
                        
                        # Execute code if requested
                        if execute_code and result['code'].strip():
                            st.subheader("Execution Result")
                            with st.spinner("Executing code..."):
                                exec_result = asyncio.run(api.execute_code(result['code'], language))
                                
                                if exec_result.get('success', False):
                                    if exec_result.get('output'):
                                        st.success("Code executed successfully!")
                                        st.code(exec_result['output'], language="text")
                                    else:
                                        st.info("Code executed successfully (no output)")
                                else:
                                    st.error("Code execution failed!")
                                    if exec_result.get('error'):
                                        st.code(exec_result['error'], language="text")
                                
                                st.caption(f"Execution time: {exec_result.get('execution_time', 0):.3f}s")
                    else:
                        st.error("Code generation failed!")
                        if result.get('explanation'):
                            st.error(result['explanation'])
                
                except Exception as e:
                    st.error(f"Error generating code: {e}")
        else:
            st.warning("Please enter a code description")

def stats_page(api: LocalMindAPI):
    """System statistics page."""
    st.title("📊 System Statistics")
    
    if st.button("Refresh Stats"):
        st.rerun()
    
    with st.spinner("Loading system statistics..."):
        try:
            stats = asyncio.run(api.get_system_stats())
            
            if 'error' in stats:
                st.error(f"Error loading stats: {stats['error']}")
                return
            
            # Overview metrics
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    "System Status",
                    "Online" if stats.get('system_initialized', False) else "Offline"
                )
            
            with col2:
                knowledge_stats = stats.get('knowledge_base', {})
                st.metric(
                    "Knowledge Entries",
                    knowledge_stats.get('knowledge_entries', 0)
                )
            
            with col3:
                st.metric(
                    "Code Snippets",
                    knowledge_stats.get('code_snippets', 0)
                )
            
            with col4:
                learning_stats = stats.get('learning', {})
                st.metric(
                    "Total Interactions",
                    learning_stats.get('total_interactions', 0)
                )
            
            # Detailed stats
            st.subheader("Detailed Statistics")
            
            # Conversation stats
            with st.expander("Conversation System"):
                conv_stats = stats.get('conversation', {})
                st.json(conv_stats)
            
            # Knowledge base stats
            with st.expander("Knowledge Base"):
                kb_stats = stats.get('knowledge_base', {})
                st.json(kb_stats)
            
            # Learning stats
            with st.expander("Learning System"):
                learn_stats = stats.get('learning', {})
                st.json(learn_stats)
            
            # Code execution stats
            with st.expander("Code Execution"):
                exec_stats = stats.get('code_execution', {})
                st.json(exec_stats)
            
        except Exception as e:
            st.error(f"Error loading system statistics: {e}")

def settings_page(api: LocalMindAPI):
    """Settings page."""
    st.title("⚙️ Settings")
    
    st.subheader("System Configuration")
    
    # Model settings
    with st.expander("Model Settings"):
        st.info("Model settings are configured in config.yaml")
        st.text("Current model: microsoft/DialoGPT-medium")
        st.text("Embedding model: sentence-transformers/all-MiniLM-L6-v2")
    
    # Execution settings
    with st.expander("Code Execution Settings"):
        st.info("Code execution settings are configured in config.yaml")
        st.text("Sandbox enabled: True")
        st.text("Timeout: 30 seconds")
        st.text("Max memory: 512 MB")
    
    # Learning settings
    with st.expander("Learning Settings"):
        st.info("Learning settings are configured in config.yaml")
        st.text("Auto-learning enabled: True")
        st.text("Learning rate: 1e-5")
        st.text("Pattern detection window: 100")
    
    # Data management
    st.subheader("Data Management")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Export Data"):
            st.info("Data export functionality would be implemented here")
    
    with col2:
        if st.button("Clear All Data"):
            if st.checkbox("I understand this will delete all data"):
                st.warning("This would clear all conversation history, knowledge base, and learning data")
    
    # About
    st.subheader("About LocalMind")
    st.markdown("""
    **LocalMind AI System v0.1.0**
    
    A completely local AI system with:
    - Continuous learning capabilities
    - Knowledge base development
    - Natural conversation interface
    - Code generation and execution
    - Self-extension framework
    - Complete local operation
    
    Built with Python, PyTorch, Transformers, and Streamlit.
    """)

if __name__ == "__main__":
    main()
