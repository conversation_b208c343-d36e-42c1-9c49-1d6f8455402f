"""Command-line interface for LocalMind AI system."""

import asyncio
import sys
from typing import Optional

import click
from rich.console import <PERSON>sole
from rich.markdown import Markdown
from rich.panel import Panel
from rich.prompt import Prompt

from .core.ai_engine import LocalMindEngine
from .utils.logging import setup_logging, get_logger

console = Console()
logger = get_logger(__name__)


class LocalMindCLI:
    """Command-line interface for LocalMind AI."""
    
    def __init__(self):
        self.engine = LocalMindEngine()
        self.console = console
        self.running = False
    
    async def start_interactive_session(self) -> None:
        """Start an interactive chat session."""
        self.console.print(Panel.fit(
            "[bold blue]LocalMind AI System[/bold blue]\n"
            "Type 'help' for commands, 'quit' to exit",
            title="Welcome"
        ))
        
        try:
            await self.engine.initialize()
            self.running = True
            
            while self.running:
                try:
                    # Get user input
                    user_input = Prompt.ask("\n[bold cyan]You[/bold cyan]").strip()
                    
                    if not user_input:
                        continue
                    
                    # Handle special commands
                    if user_input.lower() in ['quit', 'exit', 'bye']:
                        break
                    elif user_input.lower() == 'help':
                        self._show_help()
                        continue
                    elif user_input.lower() == 'clear':
                        await self.engine.clear_conversation_history()
                        self.console.print("[dim]Conversation history cleared[/dim]")
                        continue
                    elif user_input.lower().startswith('save'):
                        session_name = user_input.split(' ', 1)[1] if ' ' in user_input else None
                        saved_name = await self.engine.save_session(session_name)
                        self.console.print(f"[dim]Session saved as: {saved_name}[/dim]")
                        continue
                    elif user_input.lower().startswith('load'):
                        if ' ' in user_input:
                            session_name = user_input.split(' ', 1)[1]
                            success = await self.engine.load_session(session_name)
                            if success:
                                self.console.print(f"[dim]Session loaded: {session_name}[/dim]")
                            else:
                                self.console.print(f"[dim red]Failed to load session: {session_name}[/dim red]")
                        else:
                            self.console.print("[dim red]Usage: load <session_name>[/dim red]")
                        continue
                    elif user_input.lower().startswith('code'):
                        await self._handle_code_command(user_input)
                        continue
                    
                    # Process regular chat message
                    response = await self.engine.chat(user_input)
                    
                    # Display response
                    self.console.print(f"\n[bold green]LocalMind[/bold green]:")
                    self.console.print(Markdown(response))
                    
                except KeyboardInterrupt:
                    self.console.print("\n[dim]Use 'quit' to exit[/dim]")
                    continue
                except Exception as e:
                    self.console.print(f"[red]Error: {e}[/red]")
                    logger.error(f"CLI error: {e}")
        
        finally:
            await self.engine.shutdown()
            self.console.print("\n[dim]Goodbye![/dim]")
    
    async def _handle_code_command(self, command: str) -> None:
        """Handle code-related commands."""
        parts = command.split(' ', 2)
        
        if len(parts) < 2:
            self.console.print("[dim red]Usage: code <generate|execute> <description/code>[/dim red]")
            return
        
        action = parts[1].lower()
        
        if action == 'generate':
            if len(parts) < 3:
                self.console.print("[dim red]Usage: code generate <description>[/dim red]")
                return
            
            description = parts[2]
            language = Prompt.ask("Programming language", default="python")
            
            result = await self.engine.generate_code(description, language)
            
            self.console.print(f"\n[bold green]Generated {language} code:[/bold green]")
            self.console.print(Panel(result.get('code', ''), title="Code"))
            
            if result.get('explanation'):
                self.console.print(f"\n[bold blue]Explanation:[/bold blue]")
                self.console.print(Markdown(result['explanation']))
            
            # Ask if user wants to execute the code
            if result.get('success', False) and Prompt.ask("Execute this code?", choices=['y', 'n'], default='n') == 'y':
                exec_result = await self.engine.execute_code(result['code'], language)
                self._display_execution_result(exec_result)
        
        elif action == 'execute':
            if len(parts) < 3:
                self.console.print("[dim red]Usage: code execute <code>[/dim red]")
                return
            
            code = parts[2]
            language = Prompt.ask("Programming language", default="python")
            
            result = await self.engine.execute_code(code, language)
            self._display_execution_result(result)
        
        else:
            self.console.print("[dim red]Unknown code action. Use 'generate' or 'execute'[/dim red]")
    
    def _display_execution_result(self, result: dict) -> None:
        """Display code execution results."""
        self.console.print(f"\n[bold green]Execution Result:[/bold green]")
        
        if result.get('success', False):
            if result.get('output'):
                self.console.print(Panel(result['output'], title="Output"))
            else:
                self.console.print("[dim]No output[/dim]")
        else:
            if result.get('error'):
                self.console.print(Panel(result['error'], title="Error", border_style="red"))
        
        if result.get('execution_time'):
            self.console.print(f"[dim]Execution time: {result['execution_time']:.3f}s[/dim]")
    
    def _show_help(self) -> None:
        """Show help information."""
        help_text = """
[bold]Available Commands:[/bold]

[cyan]help[/cyan] - Show this help message
[cyan]quit/exit/bye[/cyan] - Exit the program
[cyan]clear[/cyan] - Clear conversation history
[cyan]save [name][/cyan] - Save current session
[cyan]load <name>[/cyan] - Load a saved session
[cyan]code generate <description>[/cyan] - Generate code from description
[cyan]code execute <code>[/cyan] - Execute code safely

[bold]Tips:[/bold]
- Ask questions naturally, like "Explain quantum computing"
- Request code generation: "Create a Python function to sort a list"
- Get help with debugging: "Why is my code not working?"
- Learn new concepts: "Teach me about machine learning"
        """
        self.console.print(Panel(help_text.strip(), title="Help"))


@click.command()
@click.option('--query', '-q', type=str, help='Single query mode')
@click.option('--code', '-c', type=str, help='Code generation mode')
@click.option('--language', '-l', type=str, default='python', help='Programming language')
@click.option('--execute', '-e', is_flag=True, help='Execute generated code')
def main(query: Optional[str], code: Optional[str], language: str, execute: bool):
    """LocalMind AI Command Line Interface."""
    
    # Setup logging
    setup_logging()
    
    async def run():
        cli = LocalMindCLI()
        
        if query:
            # Single query mode
            await cli.engine.initialize()
            try:
                response = await cli.engine.chat(query)
                console.print(Markdown(response))
            finally:
                await cli.engine.shutdown()
        
        elif code:
            # Code generation mode
            await cli.engine.initialize()
            try:
                result = await cli.engine.generate_code(code, language)
                
                console.print(f"[bold green]Generated {language} code:[/bold green]")
                console.print(Panel(result.get('code', ''), title="Code"))
                
                if result.get('explanation'):
                    console.print(f"\n[bold blue]Explanation:[/bold blue]")
                    console.print(Markdown(result['explanation']))
                
                if execute and result.get('success', False):
                    exec_result = await cli.engine.execute_code(result['code'], language)
                    cli._display_execution_result(exec_result)
            
            finally:
                await cli.engine.shutdown()
        
        else:
            # Interactive mode
            await cli.start_interactive_session()
    
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        console.print("\n[dim]Interrupted[/dim]")
        sys.exit(1)


if __name__ == "__main__":
    main()
