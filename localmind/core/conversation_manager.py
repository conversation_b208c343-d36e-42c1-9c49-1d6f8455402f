"""Conversation management for LocalMind AI system."""

import re
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    AutoTokenizer = None
    AutoModelForCausalLM = None
    torch = None

from .config import get_config
from ..knowledge.knowledge_base import KnowledgeBase
from ..knowledge.memory_manager import MemoryManager
from ..utils.logging import get_logger
from ..utils.security import sanitize_input

logger = get_logger(__name__)


class ConversationManager:
    """Manages conversation flow and context for the AI system."""
    
    def __init__(self, knowledge_base: KnowledgeBase, memory_manager: MemoryManager):
        self.config = get_config()
        self.knowledge_base = knowledge_base
        self.memory_manager = memory_manager
        
        # Model components
        self.tokenizer = None
        self.model = None
        self.device = None
        
        # Conversation state
        self.current_session_id = None
        self.conversation_context = []
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the conversation manager."""
        if self._initialized:
            return
        
        try:
            # Initialize dependencies
            await self.knowledge_base.initialize()
            await self.memory_manager.initialize()
            
            # Setup device
            if self.config.model.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                self.device = self.config.model.device
            
            # Load model and tokenizer
            await self._load_model()
            
            self._initialized = True
            logger.info("Conversation manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize conversation manager: {e}")
            raise
    
    async def _load_model(self) -> None:
        """Load the language model and tokenizer."""
        try:
            model_name = self.config.model.base_model
            
            logger.info(f"Loading model: {model_name}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                low_cpu_mem_usage=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            self.model.eval()
            
            logger.info(f"Model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            # Fallback to a simple response system
            logger.warning("Falling back to rule-based responses")
            self.model = None
            self.tokenizer = None
    
    async def process_message(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        session_id: Optional[str] = None
    ) -> str:
        """Process a user message and generate a response."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Sanitize input
            message = sanitize_input(message)
            
            if not message.strip():
                return "I didn't receive any message. Could you please try again?"
            
            # Set session ID
            if session_id:
                self.current_session_id = session_id
            elif not self.current_session_id:
                self.current_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Store user message
            await self.memory_manager.store_conversation(
                session_id=self.current_session_id,
                message_type="user",
                content=message,
                context=context
            )
            
            # Get conversation context
            recent_context = await self.memory_manager.get_recent_context(
                session_id=self.current_session_id,
                context_window=10
            )
            
            # Search for relevant knowledge
            relevant_knowledge = await self._search_relevant_knowledge(message)
            
            # Generate response
            response = await self._generate_response(
                message=message,
                context=context,
                conversation_history=recent_context,
                relevant_knowledge=relevant_knowledge
            )
            
            # Store AI response
            await self.memory_manager.store_conversation(
                session_id=self.current_session_id,
                message_type="assistant",
                content=response,
                context={"relevant_knowledge_count": len(relevant_knowledge)}
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return f"I apologize, but I encountered an error while processing your message: {str(e)}"
    
    async def _search_relevant_knowledge(self, message: str) -> List[Dict[str, Any]]:
        """Search for relevant knowledge to help with the response."""
        try:
            # Search knowledge base
            knowledge_results = await self.knowledge_base.search_knowledge(
                query=message,
                limit=5
            )
            
            # Search code snippets if the message seems code-related
            code_keywords = ['code', 'function', 'program', 'script', 'algorithm', 'implement']
            if any(keyword in message.lower() for keyword in code_keywords):
                code_results = await self.knowledge_base.search_code_snippets(
                    query=message,
                    limit=3
                )
                knowledge_results.extend(code_results)
            
            # Filter by relevance threshold
            relevant_knowledge = [
                item for item in knowledge_results
                if item.get('similarity', 0) > 0.7
            ]
            
            logger.info(f"Found {len(relevant_knowledge)} relevant knowledge items")
            return relevant_knowledge
            
        except Exception as e:
            logger.error(f"Error searching relevant knowledge: {e}")
            return []
    
    async def _generate_response(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        relevant_knowledge: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response to the user message."""
        try:
            # If model is available, use it
            if self.model and self.tokenizer:
                return await self._generate_model_response(
                    message, context, conversation_history, relevant_knowledge
                )
            else:
                # Fallback to rule-based responses
                return await self._generate_rule_based_response(
                    message, context, conversation_history, relevant_knowledge
                )
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    async def _generate_model_response(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        relevant_knowledge: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate response using the language model."""
        try:
            # Build conversation prompt
            prompt = self._build_conversation_prompt(
                message, context, conversation_history, relevant_knowledge
            )
            
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncate=True, max_length=1024)
            inputs = inputs.to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=self.config.model.max_tokens,
                    temperature=self.config.model.temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    no_repeat_ngram_size=3
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the new part (after the prompt)
            response = response[len(prompt):].strip()
            
            # Clean up response
            response = self._clean_response(response)
            
            return response if response else "I'm not sure how to respond to that."
            
        except Exception as e:
            logger.error(f"Error generating model response: {e}")
            return "I encountered an error while generating a response."
    
    def _build_conversation_prompt(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        relevant_knowledge: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build a conversation prompt for the model."""
        prompt_parts = []
        
        # Add system context
        prompt_parts.append("You are LocalMind, a helpful AI assistant.")
        
        # Add relevant knowledge if available
        if relevant_knowledge:
            prompt_parts.append("\nRelevant information:")
            for item in relevant_knowledge[:3]:  # Limit to top 3 items
                if 'title' in item:
                    prompt_parts.append(f"- {item['title']}: {item.get('content', '')[:200]}...")
        
        # Add conversation history
        if conversation_history:
            prompt_parts.append("\nConversation history:")
            for entry in conversation_history[-5:]:  # Last 5 messages
                role = "Human" if entry['message_type'] == 'user' else "Assistant"
                prompt_parts.append(f"{role}: {entry['content']}")
        
        # Add current message
        prompt_parts.append(f"\nHuman: {message}")
        prompt_parts.append("Assistant:")
        
        return "\n".join(prompt_parts)
    
    def _clean_response(self, response: str) -> str:
        """Clean and format the generated response."""
        # Remove any remaining prompt artifacts
        response = re.sub(r'^(Human:|Assistant:)', '', response).strip()
        
        # Remove repetitive patterns
        lines = response.split('\n')
        cleaned_lines = []
        prev_line = ""
        
        for line in lines:
            line = line.strip()
            if line and line != prev_line:
                cleaned_lines.append(line)
                prev_line = line
        
        response = '\n'.join(cleaned_lines)
        
        # Limit response length
        if len(response) > 1000:
            response = response[:1000] + "..."
        
        return response
    
    async def _generate_rule_based_response(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        relevant_knowledge: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate response using rule-based logic (fallback)."""
        message_lower = message.lower()
        
        # Greeting responses
        if any(greeting in message_lower for greeting in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return "Hello! I'm LocalMind, your local AI assistant. How can I help you today?"
        
        # Help requests
        if any(word in message_lower for word in ['help', 'what can you do', 'capabilities']):
            return ("I'm LocalMind, a local AI system with the following capabilities:\n"
                   "- Natural conversation and dialogue\n"
                   "- Code generation and understanding\n"
                   "- Knowledge base search and learning\n"
                   "- Continuous learning from interactions\n"
                   "- Local operation without internet dependency\n\n"
                   "Feel free to ask me questions, request code generation, or just have a conversation!")
        
        # Knowledge-based responses
        if relevant_knowledge:
            knowledge_text = ""
            for item in relevant_knowledge[:2]:
                if 'content' in item:
                    knowledge_text += f"{item.get('title', 'Information')}: {item['content'][:300]}...\n\n"
            
            if knowledge_text:
                return f"Based on my knowledge:\n\n{knowledge_text}"
        
        # Code-related requests
        if any(word in message_lower for word in ['code', 'program', 'function', 'script']):
            return ("I can help you with code! I can generate code in various programming languages, "
                   "explain existing code, and help debug issues. What specific programming task "
                   "would you like assistance with?")
        
        # Default response
        return ("I understand you're asking about something, but I don't have a specific response ready. "
               "Could you please rephrase your question or provide more details? I'm here to help with "
               "conversations, code generation, and answering questions based on my knowledge base.")
    
    async def clear_conversation_context(self) -> None:
        """Clear the current conversation context."""
        self.conversation_context.clear()
        self.current_session_id = None
        logger.info("Conversation context cleared")
    
    async def get_conversation_stats(self) -> Dict[str, Any]:
        """Get conversation statistics."""
        try:
            memory_stats = await self.memory_manager.get_memory_stats()
            
            return {
                'current_session_id': self.current_session_id,
                'model_loaded': self.model is not None,
                'device': self.device,
                'memory_stats': memory_stats
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation stats: {e}")
            return {'error': str(e)}
