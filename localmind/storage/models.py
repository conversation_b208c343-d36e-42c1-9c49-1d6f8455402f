"""Database models for LocalMind AI system."""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class Conversation(Base):
    """Conversation history model."""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    message_type = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    context = Column(JSON, nullable=True)
    meta_data = Column(JSON, nullable=True)


class Knowledge(Base):
    """Knowledge entries model."""
    __tablename__ = "knowledge"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(100), nullable=True)
    tags = Column(JSON, nullable=True)  # List of tags
    source = Column(String(200), nullable=True)
    confidence = Column(Float, default=1.0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime, nullable=True)


class CodeSnippet(Base):
    """Code snippets model."""
    __tablename__ = "code_snippets"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    language = Column(String(50), nullable=False)
    code = Column(Text, nullable=False)
    tags = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    execution_count = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)


class LearningEvent(Base):
    """Learning events model."""
    __tablename__ = "learning_events"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    event_type = Column(String(50), nullable=False)  # interaction, code_gen, execution, etc.
    input_data = Column(JSON, nullable=False)
    output_data = Column(JSON, nullable=False)
    feedback = Column(JSON, nullable=True)
    success = Column(Boolean, default=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    session_id = Column(String(100), nullable=True)


class Session(Base):
    """Session management model."""
    __tablename__ = "sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False)
    session_name = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_activity = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    message_count = Column(Integer, default=0)
    session_data = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True)


class VectorEmbedding(Base):
    """Vector embeddings model."""
    __tablename__ = "vector_embeddings"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    content_id = Column(String(100), nullable=False, index=True)
    content_type = Column(String(50), nullable=False)  # knowledge, code, conversation
    embedding_model = Column(String(100), nullable=False)
    embedding_vector = Column(JSON, nullable=False)  # Stored as JSON array
    meta_data = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)


class SystemMetrics(Base):
    """System performance metrics model."""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    meta_data = Column(JSON, nullable=True)
