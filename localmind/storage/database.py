"""Database management for LocalMind AI system."""

import asyncio
from pathlib import Path
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker

from .models import Base
from ..core.config import get_config
from ..utils.logging import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        self.config = get_config()
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database connections."""
        if self._initialized:
            return
        
        # Ensure database directory exists
        db_path = Path(self.config.database.sqlite_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create database URL
        db_url = f"sqlite:///{db_path}"
        async_db_url = f"sqlite+aiosqlite:///{db_path}"
        
        # Create engines
        self.engine = create_engine(db_url, echo=self.config.debug)
        self.async_engine = create_async_engine(async_db_url, echo=self.config.debug)
        
        # Create session factories
        self.session_factory = sessionmaker(bind=self.engine)
        self.async_session_factory = async_sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        self._initialized = True
        logger.info(f"Database initialized: {db_path}")
    
    async def create_tables(self) -> None:
        """Create all database tables."""
        if not self._initialized:
            await self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created")
    
    async def drop_tables(self) -> None:
        """Drop all database tables."""
        if not self._initialized:
            await self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("Database tables dropped")
    
    @asynccontextmanager
    async def get_session(self):
        """Get an async database session."""
        if not self._initialized:
            await self.initialize()
        
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a raw SQL query."""
        async with self.get_session() as session:
            result = await session.execute(text(query), params or {})
            return [dict(row._mapping) for row in result.fetchall()]
    
    async def backup_database(self, backup_path: Optional[str] = None) -> str:
        """Create a backup of the database."""
        if backup_path is None:
            timestamp = asyncio.get_event_loop().time()
            backup_path = f"{self.config.database.sqlite_path}.backup.{timestamp}"
        
        # Simple file copy for SQLite
        import shutil
        shutil.copy2(self.config.database.sqlite_path, backup_path)
        
        logger.info(f"Database backed up to: {backup_path}")
        return backup_path
    
    async def restore_database(self, backup_path: str) -> None:
        """Restore database from backup."""
        import shutil
        
        # Close connections
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()
        
        # Restore file
        shutil.copy2(backup_path, self.config.database.sqlite_path)
        
        # Reinitialize
        self._initialized = False
        await self.initialize()
        
        logger.info(f"Database restored from: {backup_path}")
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        stats = {}
        
        try:
            # Get table row counts
            tables = [
                'conversations', 'knowledge', 'code_snippets', 
                'learning_events', 'sessions', 'vector_embeddings', 
                'system_metrics'
            ]
            
            for table in tables:
                result = await self.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                stats[f"{table}_count"] = result[0]['count'] if result else 0
            
            # Get database size
            db_path = Path(self.config.database.sqlite_path)
            if db_path.exists():
                stats['database_size_bytes'] = db_path.stat().st_size
            else:
                stats['database_size_bytes'] = 0
            
            logger.info("Database statistics retrieved")
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            stats['error'] = str(e)
        
        return stats
    
    async def cleanup_old_data(self, days: int = 30) -> Dict[str, int]:
        """Clean up old data from the database."""
        cleanup_stats = {}
        
        try:
            async with self.get_session() as session:
                # Clean up old conversations
                result = await session.execute(
                    text("DELETE FROM conversations WHERE timestamp < datetime('now', '-{} days')".format(days))
                )
                cleanup_stats['conversations_deleted'] = result.rowcount
                
                # Clean up old learning events
                result = await session.execute(
                    text("DELETE FROM learning_events WHERE timestamp < datetime('now', '-{} days')".format(days))
                )
                cleanup_stats['learning_events_deleted'] = result.rowcount
                
                # Clean up old system metrics
                result = await session.execute(
                    text("DELETE FROM system_metrics WHERE timestamp < datetime('now', '-{} days')".format(days))
                )
                cleanup_stats['metrics_deleted'] = result.rowcount
                
                await session.commit()
            
            logger.info(f"Database cleanup completed: {cleanup_stats}")
            
        except Exception as e:
            logger.error(f"Error during database cleanup: {e}")
            cleanup_stats['error'] = str(e)
        
        return cleanup_stats
    
    async def shutdown(self) -> None:
        """Shutdown database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()
        
        self._initialized = False
        logger.info("Database connections closed")
