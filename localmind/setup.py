"""Setup and initialization script for LocalMind AI system."""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core.config import Config, get_config, set_config
from .utils.logging import setup_logging, get_logger
from .utils.helpers import ensure_directory

console = Console()
logger = get_logger(__name__)


class LocalMindSetup:
    """Setup manager for LocalMind AI system."""
    
    def __init__(self):
        self.config = get_config()
        self.console = console
    
    async def initialize_system(self, force: bool = False) -> bool:
        """Initialize the LocalMind AI system."""
        self.console.print("[bold blue]Initializing LocalMind AI System[/bold blue]")
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
            ) as progress:
                
                # Create directories
                task = progress.add_task("Creating directories...", total=None)
                self._create_directories()
                progress.update(task, description="✓ Directories created")
                
                # Initialize configuration
                task = progress.add_task("Setting up configuration...", total=None)
                await self._setup_configuration(force)
                progress.update(task, description="✓ Configuration ready")
                
                # Initialize database
                task = progress.add_task("Initializing database...", total=None)
                await self._initialize_database()
                progress.update(task, description="✓ Database initialized")
                
                # Download models
                task = progress.add_task("Downloading AI models...", total=None)
                await self._download_models()
                progress.update(task, description="✓ Models downloaded")
                
                # Initialize knowledge base
                task = progress.add_task("Setting up knowledge base...", total=None)
                await self._initialize_knowledge_base()
                progress.update(task, description="✓ Knowledge base ready")
                
                # Verify installation
                task = progress.add_task("Verifying installation...", total=None)
                success = await self._verify_installation()
                if success:
                    progress.update(task, description="✓ Installation verified")
                else:
                    progress.update(task, description="✗ Installation verification failed")
                    return False
            
            self.console.print("\n[bold green]✓ LocalMind AI system initialized successfully![/bold green]")
            self.console.print("\nYou can now start using LocalMind with:")
            self.console.print("  [cyan]python -m localmind.cli[/cyan]")
            self.console.print("  [cyan]python -m localmind.web[/cyan]")
            
            return True
            
        except Exception as e:
            self.console.print(f"\n[bold red]✗ Initialization failed: {e}[/bold red]")
            logger.error(f"System initialization failed: {e}")
            return False
    
    def _create_directories(self) -> None:
        """Create necessary directories."""
        directories = [
            self.config.data_dir,
            f"{self.config.data_dir}/logs",
            f"{self.config.data_dir}/models",
            f"{self.config.data_dir}/sessions",
            f"{self.config.data_dir}/knowledge",
            Path(self.config.database.sqlite_path).parent,
            Path(self.config.database.vector_db_path).parent,
        ]
        
        for directory in directories:
            ensure_directory(str(directory))
    
    async def _setup_configuration(self, force: bool = False) -> None:
        """Setup configuration files."""
        config_file = Path("config.yaml")
        
        if not config_file.exists() or force:
            self.config.save_to_file("config.yaml")
            logger.info("Configuration file created")
        else:
            logger.info("Configuration file already exists")
    
    async def _initialize_database(self) -> None:
        """Initialize the database."""
        from .storage.database import DatabaseManager
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        await db_manager.create_tables()
        logger.info("Database initialized")
    
    async def _download_models(self) -> None:
        """Download required AI models."""
        try:
            from transformers import AutoTokenizer, AutoModel
            from sentence_transformers import SentenceTransformer
            
            # Download base language model
            model_name = self.config.model.base_model
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModel.from_pretrained(model_name)
            
            # Save to local directory
            model_dir = Path(self.config.data_dir) / "models" / "base_model"
            model_dir.mkdir(parents=True, exist_ok=True)
            tokenizer.save_pretrained(model_dir)
            model.save_pretrained(model_dir)
            
            # Download embedding model
            embedding_model_name = self.config.model.embedding_model
            embedding_model = SentenceTransformer(embedding_model_name)
            embedding_dir = Path(self.config.data_dir) / "models" / "embedding_model"
            embedding_model.save(str(embedding_dir))
            
            logger.info("AI models downloaded successfully")
            
        except Exception as e:
            logger.warning(f"Model download failed, will download on first use: {e}")
    
    async def _initialize_knowledge_base(self) -> None:
        """Initialize the knowledge base."""
        from .knowledge.knowledge_base import KnowledgeBase
        
        kb = KnowledgeBase()
        await kb.initialize()
        logger.info("Knowledge base initialized")
    
    async def _verify_installation(self) -> bool:
        """Verify the installation is working."""
        try:
            from .core.ai_engine import LocalMindEngine
            
            # Create a test engine instance
            engine = LocalMindEngine()
            await engine.initialize()
            
            # Test basic functionality
            response = await engine.chat("Hello, this is a test message.")
            
            if response and len(response) > 0:
                logger.info("Installation verification successful")
                await engine.shutdown()
                return True
            else:
                logger.error("Installation verification failed: empty response")
                return False
                
        except Exception as e:
            logger.error(f"Installation verification failed: {e}")
            return False


@click.command()
@click.option('--init', is_flag=True, help='Initialize the LocalMind AI system')
@click.option('--force', is_flag=True, help='Force re-initialization')
@click.option('--config', type=str, help='Path to configuration file')
@click.option('--data-dir', type=str, help='Data directory path')
def main(init: bool, force: bool, config: Optional[str], data_dir: Optional[str]):
    """LocalMind AI System Setup."""
    
    # Setup logging
    setup_logging()
    
    if data_dir:
        # Update config with custom data directory
        current_config = get_config()
        current_config.data_dir = data_dir
        set_config(current_config)
    
    if config:
        # Load custom configuration
        custom_config = Config.load_from_file(config)
        set_config(custom_config)
    
    if init:
        setup = LocalMindSetup()
        success = asyncio.run(setup.initialize_system(force=force))
        sys.exit(0 if success else 1)
    else:
        console.print("LocalMind AI System Setup")
        console.print("Use --init to initialize the system")
        console.print("Use --help for more options")


if __name__ == "__main__":
    main()
