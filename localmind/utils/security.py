"""Security utilities for input validation and code safety."""

import re
import ast
from typing import List, Optional, <PERSON><PERSON>


def sanitize_input(text: str, max_length: int = 10000) -> str:
    """Sanitize user input to prevent injection attacks."""
    if len(text) > max_length:
        text = text[:max_length]
    
    # Remove potentially dangerous characters
    text = re.sub(r'[<>"\']', '', text)
    
    # Remove control characters except newlines and tabs
    text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
    
    return text.strip()


def validate_code(code: str, language: str = "python") -> Tuple[bool, Optional[str]]:
    """Validate code for basic safety checks."""
    if language.lower() == "python":
        return _validate_python_code(code)
    elif language.lower() in ["javascript", "js"]:
        return _validate_javascript_code(code)
    elif language.lower() == "bash":
        return _validate_bash_code(code)
    else:
        return True, None  # Allow other languages for now


def _validate_python_code(code: str) -> <PERSON><PERSON>[bool, Optional[str]]:
    """Validate Python code for safety."""
    # Check for dangerous imports and functions
    dangerous_patterns = [
        r'import\s+os',
        r'import\s+subprocess',
        r'import\s+sys',
        r'from\s+os\s+import',
        r'from\s+subprocess\s+import',
        r'__import__',
        r'eval\s*\(',
        r'exec\s*\(',
        r'open\s*\(',
        r'file\s*\(',
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, f"Potentially dangerous pattern detected: {pattern}"
    
    # Try to parse the code
    try:
        ast.parse(code)
        return True, None
    except SyntaxError as e:
        return False, f"Syntax error: {str(e)}"


def _validate_javascript_code(code: str) -> Tuple[bool, Optional[str]]:
    """Validate JavaScript code for safety."""
    dangerous_patterns = [
        r'require\s*\(',
        r'import\s+.*\s+from',
        r'eval\s*\(',
        r'Function\s*\(',
        r'document\.',
        r'window\.',
        r'process\.',
        r'global\.',
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, f"Potentially dangerous pattern detected: {pattern}"
    
    return True, None


def _validate_bash_code(code: str) -> Tuple[bool, Optional[str]]:
    """Validate Bash code for safety."""
    dangerous_patterns = [
        r'rm\s+-rf',
        r'sudo\s+',
        r'su\s+',
        r'chmod\s+',
        r'chown\s+',
        r'>/dev/',
        r'curl\s+.*\|\s*bash',
        r'wget\s+.*\|\s*bash',
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, f"Potentially dangerous pattern detected: {pattern}"
    
    return True, None
