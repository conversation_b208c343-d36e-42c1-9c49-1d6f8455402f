"""Logging configuration and utilities."""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from ..core.config import get_config


def setup_logging(log_file: Optional[str] = None) -> None:
    """Setup logging configuration."""
    config = get_config()
    
    # Remove default handler
    logger.remove()
    
    # Console handler
    logger.add(
        sys.stdout,
        level=config.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
               "<level>{message}</level>",
        colorize=True,
    )
    
    # File handler
    if log_file is None:
        log_file = Path(config.data_dir) / "logs" / "localmind.log"
    
    Path(log_file).parent.mkdir(parents=True, exist_ok=True)
    
    logger.add(
        log_file,
        level=config.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
    )


def get_logger(name: str):
    """Get a logger instance for a specific module."""
    return logger.bind(name=name)
