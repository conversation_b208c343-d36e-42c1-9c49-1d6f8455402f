"""General utility functions."""

import json
from pathlib import Path
from typing import Any, Dict, Optional


def ensure_directory(path: str) -> Path:
    """Ensure a directory exists, creating it if necessary."""
    directory = Path(path)
    directory.mkdir(parents=True, exist_ok=True)
    return directory


def load_json(file_path: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Load JSON data from file, returning default if file doesn't exist."""
    path = Path(file_path)
    if path.exists():
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return default or {}


def save_json(data: Dict[str, Any], file_path: str) -> None:
    """Save data to JSON file."""
    path = Path(file_path)
    path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
