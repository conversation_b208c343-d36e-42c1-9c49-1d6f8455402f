"""API interface for LocalMind AI system."""

from typing import Dict, Any, Optional, List
from datetime import datetime

from ..core.ai_engine import LocalMindEngine
from ..utils.logging import get_logger

logger = get_logger(__name__)


class LocalMindAPI:
    """High-level API interface for LocalMind AI system."""
    
    def __init__(self):
        self.engine = LocalMindEngine()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the API."""
        if not self._initialized:
            await self.engine.initialize()
            self._initialized = True
            logger.info("LocalMind API initialized")
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Send a chat message and get a response."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.chat(message, context)
    
    async def generate_code(
        self, 
        description: str, 
        language: str = "python",
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate code based on description."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.generate_code(description, language, context)
    
    async def execute_code(
        self, 
        code: str, 
        language: str = "python",
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute code safely."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.execute_code(code, language, context)
    
    async def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.get_conversation_history()
    
    async def clear_conversation_history(self) -> None:
        """Clear conversation history."""
        if not self._initialized:
            await self.initialize()
        
        await self.engine.clear_conversation_history()
    
    async def save_session(self, session_name: Optional[str] = None) -> str:
        """Save current session."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.save_session(session_name)
    
    async def load_session(self, session_name: str) -> bool:
        """Load a saved session."""
        if not self._initialized:
            await self.initialize()
        
        return await self.engine.load_session(session_name)
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Gather stats from various components
            conversation_stats = await self.engine.conversation_manager.get_conversation_stats()
            knowledge_stats = await self.engine.knowledge_base.get_stats()
            learning_stats = await self.engine.learning_engine.get_learning_stats()
            execution_stats = await self.engine.code_executor.get_execution_stats()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'conversation': conversation_stats,
                'knowledge_base': knowledge_stats,
                'learning': learning_stats,
                'code_execution': execution_stats,
                'system_initialized': self._initialized
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {'error': str(e)}
    
    async def shutdown(self) -> None:
        """Shutdown the API and engine."""
        if self._initialized:
            await self.engine.shutdown()
            self._initialized = False
            logger.info("LocalMind API shutdown complete")
