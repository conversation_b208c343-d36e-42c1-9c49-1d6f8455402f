{"installed_packages": ["<PERSON><PERSON><PERSON>", "certifi", "safetensors", "ipykernel", "mdurl", "pydantic", "overrides", "charset-normalizer", "bcrypt", "opentelemetry-exporter-otlp-proto-common", "build", "pytest", "filelock", "httpx", "<PERSON><PERSON><PERSON><PERSON>", "tenacity", "regex", "typing_extensions", "protobuf", "scipy", "rich", "python-dotenv", "typing-inspection", "setuptools", "plotly", "transformers", "importlib_resources", "opentelemetry-exporter-otlp-proto-grpc", "cachetools", "pydantic_core", "sympy", "pybase64", "typer", "wheel", "annotated-types", "backoff", "uvloop", "websocket-client", "grpcio", "pip", "sentence-transformers", "<PERSON><PERSON><PERSON>", "pluggy", "pyyaml", "rsa", "mmh3", "httpcore", "python-dateutil", "u<PERSON><PERSON>", "streamlit", "requests", "jsonschema", "coloredlogs", "shellingham", "googleapis-common-protos", "tokenizers", "mpmath", "notebook", "huggingface-hub", "humanfriendly", "markdown-it-py", "jsonschema-specifications", "starlette", "pygments", "urllib3", "h11", "importlib_metadata", "opentelemetry-api", "six", "greenlet", "google-auth", "fsspec", "aiosqlite", "rpds-py", "kubernetes", "opentelemetry-proto", "distro", "pypika", "scikit-learn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sqlalchemy", "posthog", "packaging", "iniconfig", "onnxruntime", "watchfiles", "durationpy", "websockets", "idna", "attrs", "referencing", "jup<PERSON><PERSON>", "httptools", "sniffio", "opentelemetry-semantic-conventions", "chromadb", "anyio", "zipp", "loguru", "asyncio", "pyasn1", "seaborn", "numpy", "pyasn1_modules", "pyproject_hooks", "flatbuffers", "opentelemetry-sdk", "hf-xet", "click", "tqdm", "requests-o<PERSON><PERSON><PERSON>"], "failed_packages": ["torch"], "timestamp": 1752664662.989436}