#!/usr/bin/env python3
"""
Basic FastAPI test for LocalMind without AI models.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any

# Create FastAPI app
app = FastAPI(
    title="LocalMind AI - Basic API",
    description="LocalMind AI REST API (Basic functionality without AI models)",
    version="1.0.0"
)

@app.get("/")
async def root():
    """Root endpoint with system status."""
    return {
        "message": "LocalMind AI - Basic API",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "ai_models": "not_loaded",
        "reason": "PyTorch not available"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "components": {
            "api": "running",
            "database": "available",
            "web_framework": "running",
            "data_science": "available",
            "vector_db": "available",
            "ai_models": "not_loaded"
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/system/info")
async def system_info():
    """Get system information."""
    try:
        import sqlalchemy
        import streamlit
        import pandas
        import numpy
        import chromadb
        
        return {
            "system": "LocalMind AI",
            "version": "1.0.0",
            "components": {
                "sqlalchemy": sqlalchemy.__version__,
                "streamlit": streamlit.__version__,
                "pandas": pandas.__version__,
                "numpy": numpy.__version__,
                "chromadb": chromadb.__version__
            },
            "status": "basic_functionality_available",
            "missing": ["torch", "sentence-transformers"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system info: {str(e)}")

@app.post("/data/process")
async def process_data(data: Dict[str, Any]):
    """Process data without AI models."""
    try:
        # Create sample data processing
        if "numbers" in data:
            numbers = data["numbers"]
            result = {
                "input": numbers,
                "sum": sum(numbers),
                "mean": np.mean(numbers),
                "std": np.std(numbers),
                "min": min(numbers),
                "max": max(numbers)
            }
            return {"status": "success", "result": result}
        
        elif "text" in data:
            text = data["text"]
            result = {
                "input": text,
                "length": len(text),
                "words": len(text.split()),
                "uppercase": text.upper(),
                "lowercase": text.lower()
            }
            return {"status": "success", "result": result}
        
        else:
            return {"status": "error", "message": "No supported data type found"}
            
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing data: {str(e)}")

@app.get("/data/sample")
async def get_sample_data():
    """Generate sample data for testing."""
    try:
        # Generate sample dataset
        data = pd.DataFrame({
            'x': np.random.randn(100),
            'y': np.random.randn(100),
            'category': np.random.choice(['A', 'B', 'C'], 100)
        })
        
        return {
            "status": "success",
            "data": {
                "shape": data.shape,
                "columns": list(data.columns),
                "sample": data.head().to_dict('records'),
                "statistics": data.describe().to_dict()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating sample data: {str(e)}")

@app.post("/knowledge/store")
async def store_knowledge(item: Dict[str, Any]):
    """Store knowledge item (basic implementation without vector DB)."""
    try:
        # Basic knowledge storage simulation
        knowledge_item = {
            "id": f"item_{datetime.now().timestamp()}",
            "content": item.get("content", ""),
            "category": item.get("category", "general"),
            "timestamp": datetime.now().isoformat(),
            "metadata": item.get("metadata", {})
        }
        
        return {
            "status": "success",
            "message": "Knowledge item stored (simulated)",
            "item": knowledge_item
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error storing knowledge: {str(e)}")

@app.get("/knowledge/search")
async def search_knowledge(query: str):
    """Search knowledge (basic implementation)."""
    try:
        # Basic search simulation
        results = [
            {
                "id": "sample_1",
                "content": f"Sample knowledge related to: {query}",
                "relevance": 0.85,
                "category": "general"
            },
            {
                "id": "sample_2", 
                "content": f"Another piece of information about: {query}",
                "relevance": 0.72,
                "category": "technical"
            }
        ]
        
        return {
            "status": "success",
            "query": query,
            "results": results,
            "note": "This is simulated search without AI models"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error searching knowledge: {str(e)}")

@app.post("/code/execute")
async def execute_code(code_data: Dict[str, Any]):
    """Execute basic Python code safely."""
    try:
        code = code_data.get("code", "")
        language = code_data.get("language", "python")
        
        if language != "python":
            return {"status": "error", "message": "Only Python code execution supported"}
        
        # Basic safe code execution
        allowed_imports = ["math", "datetime", "json", "random"]
        
        # Simple validation
        if any(dangerous in code for dangerous in ["import os", "import sys", "exec", "eval", "__"]):
            return {"status": "error", "message": "Code contains potentially dangerous operations"}
        
        # Execute in limited scope
        local_scope = {}
        exec(code, {"__builtins__": {}}, local_scope)
        
        return {
            "status": "success",
            "result": str(local_scope.get("result", "Code executed successfully")),
            "variables": {k: str(v) for k, v in local_scope.items() if not k.startswith("_")}
        }
        
    except Exception as e:
        return {"status": "error", "message": f"Code execution failed: {str(e)}"}

if __name__ == "__main__":
    print("🚀 Starting LocalMind AI Basic API...")
    print("📡 API will be available at: http://localhost:8000")
    print("📖 API documentation at: http://localhost:8000/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
