#!/usr/bin/env python3
"""
Script pentru instalarea robustă a dependințelor LocalMind AI cu retry automat.
Gestionează întreruperile de conexiune și reîncercă instalarea de unde a rămas.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

class RobustInstaller:
    def __init__(self, venv_path="localmind_env"):
        self.venv_path = Path(venv_path)
        self.pip_cmd = str(self.venv_path / "bin" / "pip")
        self.python_cmd = str(self.venv_path / "bin" / "python")
        
        # Lista de pachete în ordinea de prioritate
        self.packages = [
            # Core dependencies (mici, rapide)
            "wheel setuptools",
            "pydantic pyyaml click rich loguru python-dotenv",
            "sqlalchemy aiosqlite",
            "fastapi uvicorn",
            
            # AI dependencies (mari, pot să eșueze)
            "torch --extra-index-url https://download.pytorch.org/whl/cpu",
            "sentence-transformers",
            "streamlit",
            
            # Optional dependencies
            "pandas numpy scipy scikit-learn",
            "matplotlib seaborn plotly",
            "jupyter notebook ipykernel",
        ]
        
        self.max_retries = 5
        self.retry_delay = 10  # secunde
        
    def run_command(self, cmd, max_retries=None):
        """Execută o comandă cu retry automat."""
        if max_retries is None:
            max_retries = self.max_retries
            
        for attempt in range(max_retries):
            try:
                print(f"\n🔄 Attempt {attempt + 1}/{max_retries}: {cmd}")
                
                # Rulează comanda
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minute timeout
                )
                
                if result.returncode == 0:
                    print(f"✅ Success: {cmd}")
                    if result.stdout:
                        print(f"Output: {result.stdout[-500:]}")  # Ultimele 500 caractere
                    return True
                else:
                    print(f"❌ Failed (attempt {attempt + 1}): {cmd}")
                    print(f"Error: {result.stderr[-500:]}")  # Ultimele 500 caractere
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ Timeout (attempt {attempt + 1}): {cmd}")
            except Exception as e:
                print(f"💥 Exception (attempt {attempt + 1}): {e}")
            
            if attempt < max_retries - 1:
                print(f"⏳ Waiting {self.retry_delay} seconds before retry...")
                time.sleep(self.retry_delay)
                # Crește delay-ul progresiv
                self.retry_delay = min(self.retry_delay * 1.5, 60)
        
        print(f"💀 Failed after {max_retries} attempts: {cmd}")
        return False
    
    def check_package_installed(self, package_name):
        """Verifică dacă un pachet este deja instalat."""
        # Extrage numele de bază al pachetului
        base_name = package_name.split()[0].split("==")[0].split(">=")[0].split("<=")[0]
        
        cmd = f"{self.pip_cmd} show {base_name}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0
    
    def install_package_group(self, packages):
        """Instalează un grup de pachete cu retry."""
        print(f"\n📦 Installing package group: {packages}")
        
        # Verifică dacă pachetele sunt deja instalate
        package_list = packages.split()
        already_installed = []
        to_install = []
        
        for pkg in package_list:
            if "--" in pkg:  # Skip flags
                continue
            if self.check_package_installed(pkg):
                already_installed.append(pkg)
            else:
                to_install.append(pkg)
        
        if already_installed:
            print(f"✅ Already installed: {', '.join(already_installed)}")
        
        if not to_install:
            print("✅ All packages in group already installed!")
            return True
        
        # Construiește comanda pip
        cmd = f"{self.pip_cmd} install {packages}"
        
        # Adaugă flag-uri pentru robustețe
        cmd += " --timeout 300 --retries 3"
        
        return self.run_command(cmd)
    
    def create_venv_if_needed(self):
        """Creează virtual environment dacă nu există."""
        if not self.venv_path.exists():
            print("🔧 Creating virtual environment...")
            cmd = f"python3 -m venv {self.venv_path}"
            if not self.run_command(cmd, max_retries=2):
                print("💀 Failed to create virtual environment!")
                return False
        
        # Upgrade pip
        print("🔧 Upgrading pip...")
        cmd = f"{self.pip_cmd} install --upgrade pip"
        return self.run_command(cmd, max_retries=3)
    
    def install_all(self):
        """Instalează toate pachetele cu gestionarea erorilor."""
        print("🚀 Starting robust installation of LocalMind AI dependencies...")
        
        # Creează venv dacă e necesar
        if not self.create_venv_if_needed():
            return False
        
        # Instalează pachetele în grupuri
        success_count = 0
        total_groups = len(self.packages)
        
        for i, package_group in enumerate(self.packages, 1):
            print(f"\n{'='*60}")
            print(f"📦 Group {i}/{total_groups}: {package_group}")
            print(f"{'='*60}")
            
            if self.install_package_group(package_group):
                success_count += 1
                print(f"✅ Group {i} installed successfully!")
            else:
                print(f"❌ Group {i} failed - continuing with next group...")
                # Nu oprim instalarea, continuăm cu următorul grup
        
        # Raport final
        print(f"\n{'='*60}")
        print(f"📊 INSTALLATION SUMMARY")
        print(f"{'='*60}")
        print(f"✅ Successful groups: {success_count}/{total_groups}")
        print(f"❌ Failed groups: {total_groups - success_count}/{total_groups}")
        
        if success_count >= total_groups * 0.7:  # 70% success rate
            print("🎉 Installation mostly successful!")
            return True
        else:
            print("⚠️ Installation had significant issues, but some packages may be available.")
            return False
    
    def test_installation(self):
        """Testează instalarea cu import-uri de bază."""
        print("\n🧪 Testing installation...")
        
        test_imports = [
            "import pydantic; print('✅ Pydantic:', pydantic.__version__)",
            "import yaml; print('✅ PyYAML available')",
            "import sqlalchemy; print('✅ SQLAlchemy:', sqlalchemy.__version__)",
            "import fastapi; print('✅ FastAPI:', fastapi.__version__)",
            "import uvicorn; print('✅ Uvicorn available')",
        ]
        
        optional_imports = [
            "import torch; print('✅ PyTorch:', torch.__version__)",
            "import sentence_transformers; print('✅ Sentence Transformers available')",
            "import streamlit; print('✅ Streamlit available')",
        ]
        
        # Test core imports
        print("\n🔧 Testing core dependencies:")
        for test in test_imports:
            cmd = f"{self.python_cmd} -c \"{test}\""
            if self.run_command(cmd, max_retries=1):
                pass  # Success message already printed
            else:
                print(f"❌ Failed: {test}")
        
        # Test optional imports
        print("\n🎯 Testing optional dependencies:")
        for test in optional_imports:
            cmd = f"{self.python_cmd} -c \"{test}\""
            if self.run_command(cmd, max_retries=1):
                pass  # Success message already printed
            else:
                print(f"⚠️ Optional dependency not available: {test}")

def main():
    """Funcția principală."""
    print("🚀 LocalMind AI - Robust Dependency Installer")
    print("=" * 60)
    
    installer = RobustInstaller()
    
    try:
        # Instalează toate dependințele
        success = installer.install_all()
        
        # Testează instalarea
        installer.test_installation()
        
        if success:
            print("\n🎉 Installation completed successfully!")
            print("You can now run: python run_localmind.py")
        else:
            print("\n⚠️ Installation completed with some issues.")
            print("Some features may not be available, but basic functionality should work.")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Installation interrupted by user.")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
