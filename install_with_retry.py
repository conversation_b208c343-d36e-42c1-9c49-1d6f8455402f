#!/usr/bin/env python3
"""
Script pentru instalarea robustă a dependințelor LocalMind AI cu retry automat.
Gestionează întreruperile de conexiune și reîncercă instalarea de unde a rămas.
"""

import subprocess
import sys
import time
import os
import json
from pathlib import Path

class RobustInstaller:
    def __init__(self, venv_path="localmind_env"):
        self.venv_path = Path(venv_path)
        self.pip_cmd = str(self.venv_path / "bin" / "pip")
        self.python_cmd = str(self.venv_path / "bin" / "python")

        # Lista detaliată de pachete cu verificare individuală
        self.package_groups = {
            "core_basic": ["wheel", "setuptools", "pip"],
            "config": ["pydantic", "pyyaml", "python-dotenv"],
            "cli": ["click", "rich", "loguru"],
            "database": ["sqlalchemy", "aiosqlite"],
            "web_basic": ["fastapi", "uvicorn"],
            "ai_core": ["transformers", "tokenizers", "safetensors", "regex"],
            "vector_db": ["chromadb"],
            "torch": ["torch"],
            "sentence_transformers": ["sentence-transformers"],
            "web_ui": ["streamlit"],
            "data_science": ["pandas", "numpy", "scipy", "scikit-learn"],
            "visualization": ["matplotlib", "seaborn", "plotly"],
            "jupyter": ["jupyter", "notebook", "ipykernel"],
        }

        # Mapare pachete speciale cu comenzi custom
        self.special_installs = {
            "torch": "torch --extra-index-url https://download.pytorch.org/whl/cpu",
            "sentence-transformers": "sentence-transformers --no-deps",  # Evită dependințele grele
        }

        self.max_retries = 3
        self.retry_delay = 5  # secunde
        self.installed_packages = set()
        self.failed_packages = set()
        self.progress_file = Path("installation_progress.json")
        self.load_progress()

    def save_progress(self):
        """Salvează progresul instalării."""
        progress = {
            "installed_packages": list(self.installed_packages),
            "failed_packages": list(self.failed_packages),
            "timestamp": time.time()
        }
        try:
            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except Exception as e:
            print(f"⚠️ Could not save progress: {e}")

    def load_progress(self):
        """Încarcă progresul anterior."""
        if not self.progress_file.exists():
            return

        try:
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)

            self.failed_packages = set(progress.get("failed_packages", []))

            # Nu încărcăm installed_packages din fișier, le verificăm live
            if self.failed_packages:
                print(f"📋 Loaded previous session: {len(self.failed_packages)} previously failed packages")

        except Exception as e:
            print(f"⚠️ Could not load progress: {e}")
        
    def run_command(self, cmd, max_retries=None):
        """Execută o comandă cu retry automat."""
        if max_retries is None:
            max_retries = self.max_retries
            
        for attempt in range(max_retries):
            try:
                print(f"\n🔄 Attempt {attempt + 1}/{max_retries}: {cmd}")
                
                # Rulează comanda
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minute timeout
                )
                
                if result.returncode == 0:
                    print(f"✅ Success: {cmd}")
                    if result.stdout:
                        print(f"Output: {result.stdout[-500:]}")  # Ultimele 500 caractere
                    return True
                else:
                    print(f"❌ Failed (attempt {attempt + 1}): {cmd}")
                    print(f"Error: {result.stderr[-500:]}")  # Ultimele 500 caractere
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ Timeout (attempt {attempt + 1}): {cmd}")
            except Exception as e:
                print(f"💥 Exception (attempt {attempt + 1}): {e}")
            
            if attempt < max_retries - 1:
                print(f"⏳ Waiting {self.retry_delay} seconds before retry...")
                time.sleep(self.retry_delay)
                # Crește delay-ul progresiv
                self.retry_delay = min(self.retry_delay * 1.5, 60)
        
        print(f"💀 Failed after {max_retries} attempts: {cmd}")
        return False
    
    def get_installed_packages(self):
        """Obține lista completă de pachete instalate."""
        if self.installed_packages:
            return self.installed_packages

        print("🔍 Checking currently installed packages...")
        cmd = f"{self.pip_cmd} list --format=freeze"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if '==' in line:
                    package_name = line.split('==')[0].lower()
                    self.installed_packages.add(package_name)

        print(f"✅ Found {len(self.installed_packages)} installed packages")
        return self.installed_packages

    def is_package_installed(self, package_name):
        """Verifică dacă un pachet specific este instalat."""
        # Normalizează numele pachetului
        clean_name = package_name.lower().replace('_', '-').replace(' ', '')
        installed = self.get_installed_packages()

        # Verifică variante ale numelui
        variants = [
            clean_name,
            clean_name.replace('-', '_'),
            clean_name.replace('_', '-'),
        ]

        for variant in variants:
            if variant in installed:
                return True

        # Verificare suplimentară cu pip show
        cmd = f"{self.pip_cmd} show {package_name}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0
    
    def install_package_group(self, group_name, packages):
        """Instalează un grup de pachete cu verificare individuală."""
        print(f"\n📦 Processing group '{group_name}': {packages}")

        # Verifică fiecare pachet individual
        already_installed = []
        to_install = []

        for pkg in packages:
            if self.is_package_installed(pkg):
                already_installed.append(pkg)
                print(f"  ✅ {pkg} - already installed")
            elif pkg in self.failed_packages:
                print(f"  ⏭️ {pkg} - skipping (previously failed)")
            else:
                to_install.append(pkg)
                print(f"  📥 {pkg} - needs installation")

        if not to_install:
            print(f"✅ Group '{group_name}' - all packages already installed!")
            return True

        # Instalează pachetele care lipsesc
        success_count = 0
        for pkg in to_install:
            if self.install_single_package(pkg):
                success_count += 1
                self.installed_packages.add(pkg.lower().replace('_', '-'))
            else:
                self.failed_packages.add(pkg)

            # Salvează progresul după fiecare pachet
            self.save_progress()

        success_rate = success_count / len(to_install) if to_install else 1.0
        print(f"📊 Group '{group_name}' result: {success_count}/{len(to_install)} packages installed ({success_rate:.1%})")

        return success_rate >= 0.5  # Consideră succes dacă >50% din pachete s-au instalat

    def install_single_package(self, package):
        """Instalează un singur pachet cu retry."""
        print(f"\n🔧 Installing {package}...")

        # Verifică dacă avem o comandă specială pentru acest pachet
        if package in self.special_installs:
            install_cmd = self.special_installs[package]
        else:
            install_cmd = package

        # Construiește comanda completă
        cmd = f"{self.pip_cmd} install {install_cmd} --timeout 300"

        return self.run_command(cmd)
    
    def create_venv_if_needed(self):
        """Creează virtual environment dacă nu există."""
        if not self.venv_path.exists():
            print("🔧 Creating virtual environment...")
            cmd = f"python3 -m venv {self.venv_path}"
            if not self.run_command(cmd, max_retries=2):
                print("💀 Failed to create virtual environment!")
                return False
        
        # Upgrade pip
        print("🔧 Upgrading pip...")
        cmd = f"{self.pip_cmd} install --upgrade pip"
        return self.run_command(cmd, max_retries=3)
    
    def install_all(self):
        """Instalează toate pachetele cu gestionarea erorilor."""
        print("🚀 Starting robust installation of LocalMind AI dependencies...")
        print("🔍 This will check what's already installed and only install missing packages.")

        # Creează venv dacă e necesar
        if not self.create_venv_if_needed():
            return False

        # Obține lista de pachete instalate
        self.get_installed_packages()

        # Instalează pachetele în grupuri
        success_count = 0
        total_groups = len(self.package_groups)

        for i, (group_name, packages) in enumerate(self.package_groups.items(), 1):
            print(f"\n{'='*70}")
            print(f"📦 Group {i}/{total_groups}: {group_name}")
            print(f"   Packages: {', '.join(packages)}")
            print(f"{'='*70}")

            if self.install_package_group(group_name, packages):
                success_count += 1
                print(f"✅ Group '{group_name}' completed successfully!")
            else:
                print(f"⚠️ Group '{group_name}' had issues - continuing with next group...")

        # Raport final
        print(f"\n{'='*70}")
        print(f"📊 FINAL INSTALLATION SUMMARY")
        print(f"{'='*70}")
        print(f"✅ Successful groups: {success_count}/{total_groups}")
        print(f"❌ Problematic groups: {total_groups - success_count}/{total_groups}")

        if self.failed_packages:
            print(f"❌ Failed packages: {', '.join(sorted(self.failed_packages))}")

        # Actualizează lista de pachete instalate
        self.installed_packages.clear()
        final_installed = self.get_installed_packages()
        print(f"📦 Total packages now installed: {len(final_installed)}")

        if success_count >= total_groups * 0.6:  # 60% success rate
            print("🎉 Installation successful! Most packages are available.")
            return True
        else:
            print("⚠️ Installation had issues, but basic functionality should work.")
            return False
    
    def test_installation(self):
        """Testează instalarea cu import-uri de bază."""
        print("\n🧪 Testing installation...")
        
        test_imports = [
            "import pydantic; print('✅ Pydantic:', pydantic.__version__)",
            "import yaml; print('✅ PyYAML available')",
            "import sqlalchemy; print('✅ SQLAlchemy:', sqlalchemy.__version__)",
            "import fastapi; print('✅ FastAPI:', fastapi.__version__)",
            "import uvicorn; print('✅ Uvicorn available')",
        ]
        
        optional_imports = [
            "import torch; print('✅ PyTorch:', torch.__version__)",
            "import sentence_transformers; print('✅ Sentence Transformers available')",
            "import streamlit; print('✅ Streamlit available')",
        ]
        
        # Test core imports
        print("\n🔧 Testing core dependencies:")
        for test in test_imports:
            cmd = f"{self.python_cmd} -c \"{test}\""
            if self.run_command(cmd, max_retries=1):
                pass  # Success message already printed
            else:
                print(f"❌ Failed: {test}")
        
        # Test optional imports
        print("\n🎯 Testing optional dependencies:")
        for test in optional_imports:
            cmd = f"{self.python_cmd} -c \"{test}\""
            if self.run_command(cmd, max_retries=1):
                pass  # Success message already printed
            else:
                print(f"⚠️ Optional dependency not available: {test}")

def main():
    """Funcția principală."""
    print("🚀 LocalMind AI - Robust Dependency Installer")
    print("=" * 60)
    
    installer = RobustInstaller()
    
    try:
        # Instalează toate dependințele
        success = installer.install_all()
        
        # Testează instalarea
        installer.test_installation()
        
        if success:
            print("\n🎉 Installation completed successfully!")
            print("You can now run: python run_localmind.py")
        else:
            print("\n⚠️ Installation completed with some issues.")
            print("Some features may not be available, but basic functionality should work.")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Installation interrupted by user.")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
