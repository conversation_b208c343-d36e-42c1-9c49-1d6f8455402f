#!/usr/bin/env python3
"""
LocalMind AI System Demonstration

This script demonstrates the key capabilities of the LocalMind AI system.
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

async def demonstrate_localmind():
    """Demonstrate LocalMind AI capabilities."""
    print("🧠 LocalMind AI System Demonstration")
    print("=" * 50)
    
    try:
        from localmind.interfaces.api import LocalMindAPI
        
        # Initialize the system
        print("🚀 Initializing LocalMind AI system...")
        api = LocalMindAPI()
        await api.initialize()
        print("✅ System initialized successfully!\n")
        
        # Demonstration 1: Basic Conversation
        print("💬 DEMONSTRATION 1: Natural Conversation")
        print("-" * 40)
        
        conversations = [
            "Hello! What is LocalMind AI?",
            "What are your main capabilities?",
            "How do you learn and improve over time?"
        ]
        
        for question in conversations:
            print(f"User: {question}")
            response = await api.chat(question)
            print(f"LocalMind: {response}\n")
        
        # Demonstration 2: Code Generation
        print("💻 DEMONSTRATION 2: Code Generation")
        print("-" * 40)
        
        code_requests = [
            ("Create a Python function that calculates the factorial of a number", "python"),
            ("Write a JavaScript function to reverse a string", "javascript"),
            ("Create a bash script that lists files in a directory", "bash")
        ]
        
        for description, language in code_requests:
            print(f"Request: {description} ({language})")
            result = await api.generate_code(description, language)
            
            if result.get('success'):
                print("Generated Code:")
                print(result['code'])
                print(f"Explanation: {result.get('explanation', 'No explanation provided')}\n")
            else:
                print(f"❌ Code generation failed: {result.get('explanation', 'Unknown error')}\n")
        
        # Demonstration 3: Code Execution
        print("⚡ DEMONSTRATION 3: Code Execution")
        print("-" * 40)
        
        # Generate and execute a simple Python program
        print("Generating and executing a Python program...")
        code_result = await api.generate_code(
            "Create a function that prints the first 5 fibonacci numbers",
            "python"
        )
        
        if code_result.get('success'):
            print("Generated Code:")
            print(code_result['code'])
            
            # Execute the code
            exec_result = await api.execute_code(code_result['code'], "python")
            
            if exec_result.get('success'):
                print("✅ Execution successful!")
                print("Output:")
                print(exec_result.get('output', 'No output'))
                print(f"Execution time: {exec_result.get('execution_time', 0):.3f} seconds\n")
            else:
                print("❌ Execution failed!")
                print(f"Error: {exec_result.get('error', 'Unknown error')}\n")
        
        # Demonstration 4: Learning and Knowledge
        print("🧠 DEMONSTRATION 4: Learning and Knowledge Management")
        print("-" * 40)
        
        # Show how the system learns from interactions
        print("Teaching LocalMind about a new concept...")
        await api.chat("Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.")
        
        print("Now asking about the concept...")
        response = await api.chat("What is machine learning?")
        print(f"LocalMind: {response}\n")
        
        # Demonstration 5: System Statistics
        print("📊 DEMONSTRATION 5: System Statistics")
        print("-" * 40)
        
        stats = await api.get_system_stats()
        
        if 'error' not in stats:
            print("System Status:")
            print(f"  Initialized: {'✅' if stats.get('system_initialized') else '❌'}")
            print(f"  Timestamp: {stats.get('timestamp', 'Unknown')}")
            
            kb_stats = stats.get('knowledge_base', {})
            print(f"\nKnowledge Base:")
            print(f"  Knowledge Entries: {kb_stats.get('knowledge_entries', 0)}")
            print(f"  Code Snippets: {kb_stats.get('code_snippets', 0)}")
            
            learning_stats = stats.get('learning', {})
            print(f"\nLearning System:")
            print(f"  Total Interactions: {learning_stats.get('total_interactions', 0)}")
            print(f"  Successful Learnings: {learning_stats.get('successful_learnings', 0)}")
            
            conv_stats = stats.get('conversation', {})
            print(f"\nConversation System:")
            print(f"  Model Loaded: {'✅' if conv_stats.get('model_loaded') else '❌'}")
            print(f"  Device: {conv_stats.get('device', 'Unknown')}")
        
        # Demonstration 6: Session Management
        print("\n💾 DEMONSTRATION 6: Session Management")
        print("-" * 40)
        
        # Save current session
        session_name = await api.save_session("demo_session")
        print(f"✅ Session saved as: {session_name}")
        
        # Get conversation history
        history = await api.get_conversation_history()
        print(f"📝 Conversation history contains {len(history)} messages")
        
        # Clear history
        await api.clear_conversation_history()
        print("🗑️  Conversation history cleared")
        
        # Load session back
        success = await api.load_session("demo_session")
        if success:
            print("✅ Session loaded successfully")
            restored_history = await api.get_conversation_history()
            print(f"📝 Restored {len(restored_history)} messages from session")
        
        print("\n🎉 DEMONSTRATION COMPLETE!")
        print("=" * 50)
        print("\nKey Features Demonstrated:")
        print("✅ Natural language conversation")
        print("✅ Multi-language code generation")
        print("✅ Safe code execution")
        print("✅ Continuous learning from interactions")
        print("✅ Knowledge base management")
        print("✅ Session persistence")
        print("✅ System monitoring and statistics")
        
        print("\nNext Steps:")
        print("🌐 Try the web interface: python run_localmind.py web")
        print("💬 Use the CLI: python run_localmind.py cli")
        print("📖 Read the documentation in README.md")
        print("🔧 Customize settings in config.yaml")
        
        # Cleanup
        await api.shutdown()
        
    except ImportError as e:
        print(f"❌ Error: LocalMind system not properly installed")
        print(f"Details: {e}")
        print("\nPlease run: python install.py")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        print("\nThis might be due to:")
        print("- System not properly initialized")
        print("- Missing dependencies")
        print("- Configuration issues")
        print("\nTry running: python install.py")

def main():
    """Main function."""
    print("Starting LocalMind AI demonstration...")
    print("This will showcase the key capabilities of the system.\n")
    
    try:
        asyncio.run(demonstrate_localmind())
    except KeyboardInterrupt:
        print("\n\n❌ Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
