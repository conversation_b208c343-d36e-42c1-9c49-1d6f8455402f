#!/usr/bin/env python3
"""
Script robust pentru instalarea PyTorch cu multiple strategii de retry.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

class PyTorchInstaller:
    def __init__(self, venv_path="localmind_env"):
        self.venv_path = Path(venv_path)
        self.pip_cmd = str(self.venv_path / "bin" / "pip")
        self.python_cmd = str(self.venv_path / "bin" / "python")
        
        # Multiple installation strategies
        self.strategies = [
            {
                "name": "CPU-only with resume",
                "command": f"{self.pip_cmd} install torch --extra-index-url https://download.pytorch.org/whl/cpu --timeout 1800 --retries 10"
            },
            {
                "name": "CPU-only smaller chunks",
                "command": f"{self.pip_cmd} install torch --extra-index-url https://download.pytorch.org/whl/cpu --timeout 3600 --retries 5 --no-cache-dir"
            },
            {
                "name": "Default PyPI",
                "command": f"{self.pip_cmd} install torch --timeout 3600 --retries 10"
            },
            {
                "name": "Force reinstall",
                "command": f"{self.pip_cmd} install torch --force-reinstall --no-deps --timeout 3600"
            }
        ]
        
    def check_pytorch_installed(self):
        """Verifică dacă PyTorch este deja instalat."""
        try:
            result = subprocess.run(
                f"{self.python_cmd} -c 'import torch; print(torch.__version__)'",
                shell=True,
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ PyTorch already installed: {version}")
                return True
            return False
        except:
            return False
    
    def run_command_with_progress(self, cmd, timeout=3600):
        """Rulează comandă cu afișare progres."""
        print(f"🔄 Running: {cmd}")
        print(f"⏱️ Timeout: {timeout} seconds")
        
        try:
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            output_lines = []
            start_time = time.time()
            
            while True:
                # Check if process finished
                if process.poll() is not None:
                    break
                
                # Check timeout
                if time.time() - start_time > timeout:
                    print("⏰ Timeout reached, terminating process...")
                    process.terminate()
                    time.sleep(5)
                    if process.poll() is None:
                        process.kill()
                    return False, "Timeout"
                
                # Read output
                try:
                    line = process.stdout.readline()
                    if line:
                        output_lines.append(line.strip())
                        # Show progress for downloads
                        if "Downloading" in line or "━" in line or "MB" in line:
                            print(f"📥 {line.strip()}")
                        elif "Installing" in line:
                            print(f"🔧 {line.strip()}")
                        elif "Successfully installed" in line:
                            print(f"✅ {line.strip()}")
                except:
                    pass
                
                time.sleep(0.1)
            
            # Get final return code
            return_code = process.wait()
            
            if return_code == 0:
                print("✅ Command completed successfully!")
                return True, "Success"
            else:
                print(f"❌ Command failed with return code: {return_code}")
                return False, f"Return code: {return_code}"
                
        except Exception as e:
            print(f"💥 Exception during command execution: {e}")
            return False, str(e)
    
    def install_pytorch(self):
        """Încearcă să instaleze PyTorch cu multiple strategii."""
        print("🧠 PyTorch Installation - Multiple Strategy Approach")
        print("=" * 60)
        
        # Check if already installed
        if self.check_pytorch_installed():
            return True
        
        # Try each strategy
        for i, strategy in enumerate(self.strategies, 1):
            print(f"\n🎯 Strategy {i}/{len(self.strategies)}: {strategy['name']}")
            print("-" * 50)
            
            success, message = self.run_command_with_progress(
                strategy['command'], 
                timeout=3600  # 1 hour timeout
            )
            
            if success:
                print(f"🎉 Strategy {i} succeeded!")
                
                # Verify installation
                if self.check_pytorch_installed():
                    print("✅ PyTorch installation verified!")
                    return True
                else:
                    print("⚠️ Installation completed but verification failed")
            else:
                print(f"❌ Strategy {i} failed: {message}")
                print("⏳ Waiting 30 seconds before next strategy...")
                time.sleep(30)
        
        print("💀 All strategies failed!")
        return False
    
    def install_dependencies_after_pytorch(self):
        """Instalează dependințele care necesită PyTorch."""
        print("\n🔧 Installing PyTorch-dependent packages...")
        
        dependencies = [
            "sentence-transformers",
            "torchvision", 
            "torchaudio"
        ]
        
        for dep in dependencies:
            print(f"\n📦 Installing {dep}...")
            success, message = self.run_command_with_progress(
                f"{self.pip_cmd} install {dep} --timeout 1800",
                timeout=1800
            )
            
            if success:
                print(f"✅ {dep} installed successfully!")
            else:
                print(f"⚠️ {dep} installation failed: {message}")
    
    def test_pytorch_functionality(self):
        """Testează funcționalitatea PyTorch."""
        print("\n🧪 Testing PyTorch functionality...")
        
        test_script = '''
import torch
import numpy as np

print("✅ PyTorch imported successfully!")
print(f"PyTorch version: {torch.__version__}")

# Test basic tensor operations
x = torch.randn(3, 3)
y = torch.randn(3, 3)
z = torch.matmul(x, y)

print(f"✅ Tensor operations work!")
print(f"Tensor shape: {z.shape}")

# Test if CUDA is available (should be False for CPU-only)
print(f"CUDA available: {torch.cuda.is_available()}")

# Test sentence transformers if available
try:
    from sentence_transformers import SentenceTransformer
    print("✅ Sentence Transformers available!")
except ImportError:
    print("⚠️ Sentence Transformers not available")

print("🎉 PyTorch functionality test completed!")
'''
        
        success, message = self.run_command_with_progress(
            f"{self.python_cmd} -c \"{test_script}\"",
            timeout=60
        )
        
        return success

def main():
    """Funcția principală."""
    installer = PyTorchInstaller()
    
    print("🚀 Starting PyTorch installation process...")
    
    # Install PyTorch
    if installer.install_pytorch():
        print("\n🎉 PyTorch installation successful!")
        
        # Install dependent packages
        installer.install_dependencies_after_pytorch()
        
        # Test functionality
        if installer.test_pytorch_functionality():
            print("\n✅ All tests passed! PyTorch is ready to use.")
            return True
        else:
            print("\n⚠️ PyTorch installed but tests failed.")
            return False
    else:
        print("\n❌ PyTorch installation failed!")
        print("\n🔧 Alternative options:")
        print("1. Try manual installation with better internet connection")
        print("2. Download PyTorch wheel manually and install locally")
        print("3. Use conda instead of pip")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
