
import streamlit as st
import pandas as pd
import numpy as np

st.title("🧠 LocalMind AI - Basic Interface Test")
st.write("This is a test of the LocalMind web interface without AI models.")

st.header("System Status")
st.success("✅ Streamlit interface is working!")
st.info("ℹ️ AI models not loaded (PyTorch not available)")

st.header("Basic Functionality")
if st.button("Test Data Processing"):
    # Generate some test data
    data = pd.DataFrame({
        'x': np.random.randn(100),
        'y': np.random.randn(100)
    })
    
    st.subheader("Sample Data")
    st.dataframe(data.head())
    
    st.subheader("Data Visualization")
    st.line_chart(data)
    
    st.success("✅ Data processing and visualization working!")

st.header("Next Steps")
st.write("""
To enable full AI functionality:
1. Install PyTorch: `pip install torch`
2. Restart the system
3. AI models will be automatically loaded
""")
