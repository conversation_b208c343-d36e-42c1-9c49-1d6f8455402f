[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "localmind"
version = "0.1.0"
description = "A local AI system with continuous learning and self-extension capabilities"
authors = [{name = "LocalMind Developer", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "sentence-transformers>=2.2.0",
    "chromadb>=0.4.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.22.0",
    "streamlit>=1.25.0",
    "sqlalchemy>=2.0.0",
    "pydantic>=2.0.0",
    "click>=8.1.0",
    "rich>=13.4.0",
    "loguru>=0.7.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.7.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
]
full = [
    "faiss-cpu>=1.7.4",
    "nltk>=3.8",
    "spacy>=3.6.0",
    "docker>=6.1.0",
    "psutil>=5.9.0",
]

[project.scripts]
localmind = "localmind.cli:main"
localmind-web = "localmind.web:main"
localmind-setup = "localmind.setup:main"

[project.urls]
Homepage = "https://github.com/yourusername/localmind"
Repository = "https://github.com/yourusername/localmind"
Documentation = "https://localmind.readthedocs.io"

[tool.setuptools.packages.find]
where = ["."]
include = ["localmind*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
