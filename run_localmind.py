#!/usr/bin/env python3
"""
LocalMind AI System Launcher

Quick launcher script for the LocalMind AI system with different interfaces.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """Main launcher function."""
    parser = argparse.ArgumentParser(
        description="LocalMind AI System Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_localmind.py cli                    # Start CLI interface
  python run_localmind.py web                    # Start web interface
  python run_localmind.py chat "Hello!"          # Quick chat
  python run_localmind.py code "sort function"   # Quick code generation
  python run_localmind.py setup                  # Initialize system
  python run_localmind.py test                   # Run tests
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['cli', 'web', 'chat', 'code', 'setup', 'test', 'stats'],
        help='Interface mode to launch'
    )
    
    parser.add_argument(
        'message',
        nargs='?',
        help='Message for chat/code modes'
    )
    
    parser.add_argument(
        '--language', '-l',
        default='python',
        help='Programming language for code generation'
    )
    
    parser.add_argument(
        '--execute', '-e',
        action='store_true',
        help='Execute generated code'
    )
    
    parser.add_argument(
        '--init',
        action='store_true',
        help='Initialize system (for setup mode)'
    )
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'cli':
            launch_cli()
        elif args.mode == 'web':
            launch_web()
        elif args.mode == 'chat':
            if not args.message:
                print("Error: Message required for chat mode")
                sys.exit(1)
            asyncio.run(quick_chat(args.message))
        elif args.mode == 'code':
            if not args.message:
                print("Error: Description required for code mode")
                sys.exit(1)
            asyncio.run(quick_code(args.message, args.language, args.execute))
        elif args.mode == 'setup':
            launch_setup(args.init)
        elif args.mode == 'test':
            run_tests()
        elif args.mode == 'stats':
            asyncio.run(show_stats())
            
    except KeyboardInterrupt:
        print("\n\nGoodbye!")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def launch_cli():
    """Launch CLI interface."""
    import subprocess
    subprocess.run([sys.executable, "-m", "localmind.cli"])

def launch_web():
    """Launch web interface."""
    import subprocess
    print("🌐 Starting LocalMind web interface...")
    print("📍 Open your browser to: http://localhost:8000")
    subprocess.run([sys.executable, "-m", "streamlit", "run", "localmind/web.py", "--server.port=8000"])

async def quick_chat(message: str):
    """Quick chat interface."""
    print("🧠 LocalMind AI - Quick Chat")
    print("=" * 40)
    
    try:
        from localmind.interfaces.api import LocalMindAPI
        
        api = LocalMindAPI()
        await api.initialize()
        
        print(f"You: {message}")
        print("\nLocalMind: ", end="", flush=True)
        
        response = await api.chat(message)
        print(response)
        
        await api.shutdown()
        
    except Exception as e:
        print(f"Error: {e}")

async def quick_code(description: str, language: str, execute: bool):
    """Quick code generation interface."""
    print("💻 LocalMind AI - Quick Code Generation")
    print("=" * 50)
    
    try:
        from localmind.interfaces.api import LocalMindAPI
        
        api = LocalMindAPI()
        await api.initialize()
        
        print(f"Description: {description}")
        print(f"Language: {language}")
        print("\nGenerating code...\n")
        
        result = await api.generate_code(description, language)
        
        if result.get('success', False):
            print("Generated Code:")
            print("-" * 40)
            print(result['code'])
            print("-" * 40)
            
            if result.get('explanation'):
                print(f"\nExplanation:\n{result['explanation']}")
            
            if execute and result['code'].strip():
                print(f"\nExecuting {language} code...")
                exec_result = await api.execute_code(result['code'], language)
                
                if exec_result.get('success', False):
                    print("✅ Execution successful!")
                    if exec_result.get('output'):
                        print(f"Output:\n{exec_result['output']}")
                else:
                    print("❌ Execution failed!")
                    if exec_result.get('error'):
                        print(f"Error: {exec_result['error']}")
        else:
            print("❌ Code generation failed!")
            if result.get('explanation'):
                print(f"Error: {result['explanation']}")
        
        await api.shutdown()
        
    except Exception as e:
        print(f"Error: {e}")

def launch_setup(init_system: bool):
    """Launch setup."""
    import subprocess
    
    if init_system:
        subprocess.run([sys.executable, "-m", "localmind.setup", "--init"])
    else:
        subprocess.run([sys.executable, "-m", "localmind.setup", "--help"])

def run_tests():
    """Run tests."""
    import subprocess
    
    print("🧪 Running LocalMind tests...")
    
    try:
        subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"], check=True)
        print("✅ All tests passed!")
    except subprocess.CalledProcessError:
        print("❌ Some tests failed!")
    except FileNotFoundError:
        print("❌ pytest not found. Install with: pip install pytest")

async def show_stats():
    """Show system statistics."""
    print("📊 LocalMind AI - System Statistics")
    print("=" * 40)
    
    try:
        from localmind.interfaces.api import LocalMindAPI
        
        api = LocalMindAPI()
        await api.initialize()
        
        stats = await api.get_system_stats()
        
        if 'error' in stats:
            print(f"Error getting stats: {stats['error']}")
            return
        
        print(f"System Status: {'🟢 Online' if stats.get('system_initialized') else '🔴 Offline'}")
        print(f"Timestamp: {stats.get('timestamp', 'Unknown')}")
        
        # Knowledge base stats
        kb_stats = stats.get('knowledge_base', {})
        print(f"\n📚 Knowledge Base:")
        print(f"  Knowledge Entries: {kb_stats.get('knowledge_entries', 0)}")
        print(f"  Code Snippets: {kb_stats.get('code_snippets', 0)}")
        print(f"  Total Items: {kb_stats.get('total_items', 0)}")
        
        # Learning stats
        learning_stats = stats.get('learning', {})
        print(f"\n🧠 Learning System:")
        print(f"  Total Interactions: {learning_stats.get('total_interactions', 0)}")
        print(f"  Successful Learnings: {learning_stats.get('successful_learnings', 0)}")
        print(f"  Failed Learnings: {learning_stats.get('failed_learnings', 0)}")
        
        # Conversation stats
        conv_stats = stats.get('conversation', {})
        print(f"\n💬 Conversation System:")
        print(f"  Current Session: {conv_stats.get('current_session_id', 'None')}")
        print(f"  Model Loaded: {'✅' if conv_stats.get('model_loaded') else '❌'}")
        print(f"  Device: {conv_stats.get('device', 'Unknown')}")
        
        # Code execution stats
        exec_stats = stats.get('code_execution', {})
        print(f"\n💻 Code Execution:")
        print(f"  Sandbox Enabled: {'✅' if exec_stats.get('sandbox_enabled') else '❌'}")
        print(f"  Timeout: {exec_stats.get('timeout_seconds', 0)}s")
        print(f"  Supported Languages: {', '.join(exec_stats.get('supported_languages', []))}")
        
        await api.shutdown()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
