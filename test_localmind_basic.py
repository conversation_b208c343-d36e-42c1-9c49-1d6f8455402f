#!/usr/bin/env python3
"""
Test basic LocalMind functionality without AI models.
This demonstrates the core system components that work without PyTorch.
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_basic_components():
    """Test basic system components that don't require AI models."""
    print("🧪 Testing LocalMind Basic Components")
    print("=" * 50)
    
    results = {}
    
    # Test 1: Configuration System
    print("\n1️⃣ Testing Configuration System...")
    try:
        # Import only the config module directly
        import yaml
        from localmind.core.config import LocalMindConfig
        
        config = LocalMindConfig()
        print(f"✅ Configuration loaded successfully!")
        print(f"   Data directory: {config.data_dir}")
        print(f"   Log level: {config.log_level}")
        print(f"   Debug mode: {config.debug}")
        results['config'] = True
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        results['config'] = False
    
    # Test 2: Database System (without AI dependencies)
    print("\n2️⃣ Testing Database System...")
    try:
        import sqlalchemy
        import aiosqlite
        print("✅ Database dependencies available!")
        print(f"   SQLAlchemy version: {sqlalchemy.__version__}")
        results['database'] = True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        results['database'] = False
    
    # Test 3: Web Framework
    print("\n3️⃣ Testing Web Framework...")
    try:
        import fastapi
        import uvicorn
        import streamlit as st
        print("✅ Web frameworks available!")
        print(f"   FastAPI version: {fastapi.__version__}")
        print(f"   Streamlit version: {st.__version__}")
        results['web'] = True
    except Exception as e:
        print(f"❌ Web framework test failed: {e}")
        results['web'] = False
    
    # Test 4: Data Science Stack
    print("\n4️⃣ Testing Data Science Stack...")
    try:
        import pandas as pd
        import numpy as np
        import matplotlib
        import seaborn
        import plotly
        print("✅ Data science stack available!")
        print(f"   Pandas version: {pd.__version__}")
        print(f"   NumPy version: {np.__version__}")
        print(f"   Matplotlib version: {matplotlib.__version__}")
        results['datascience'] = True
    except Exception as e:
        print(f"❌ Data science test failed: {e}")
        results['datascience'] = False
    
    # Test 5: Vector Database
    print("\n5️⃣ Testing Vector Database...")
    try:
        import chromadb
        print("✅ ChromaDB available!")
        print(f"   ChromaDB version: {chromadb.__version__}")
        results['vectordb'] = True
    except Exception as e:
        print(f"❌ Vector database test failed: {e}")
        results['vectordb'] = False
    
    # Test 6: File System Setup
    print("\n6️⃣ Testing File System Setup...")
    try:
        data_dir = Path("data")
        required_dirs = ["logs", "models", "sessions", "knowledge", "plugins"]
        
        for dir_name in required_dirs:
            dir_path = data_dir / dir_name
            if dir_path.exists():
                print(f"   ✅ {dir_name}/ directory exists")
            else:
                print(f"   ⚠️ {dir_name}/ directory missing")
        
        results['filesystem'] = True
    except Exception as e:
        print(f"❌ File system test failed: {e}")
        results['filesystem'] = False
    
    # Test 7: Basic Code Execution (without AI)
    print("\n7️⃣ Testing Basic Code Execution...")
    try:
        # Test Python code execution
        test_code = """
def hello_localmind():
    return "Hello from LocalMind!"

result = hello_localmind()
print(f"Test result: {result}")
"""
        exec(test_code)
        print("✅ Basic code execution works!")
        results['execution'] = True
    except Exception as e:
        print(f"❌ Code execution test failed: {e}")
        results['execution'] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.upper():15} {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests >= total_tests * 0.7:
        print("🎉 LocalMind basic system is functional!")
        return True
    else:
        print("⚠️ LocalMind has significant issues.")
        return False

def test_streamlit_interface():
    """Test if we can start a basic Streamlit interface."""
    print("\n🌐 Testing Streamlit Interface...")
    
    # Create a simple test Streamlit app
    test_app_content = '''
import streamlit as st
import pandas as pd
import numpy as np

st.title("🧠 LocalMind AI - Basic Interface Test")
st.write("This is a test of the LocalMind web interface without AI models.")

st.header("System Status")
st.success("✅ Streamlit interface is working!")
st.info("ℹ️ AI models not loaded (PyTorch not available)")

st.header("Basic Functionality")
if st.button("Test Data Processing"):
    # Generate some test data
    data = pd.DataFrame({
        'x': np.random.randn(100),
        'y': np.random.randn(100)
    })
    
    st.subheader("Sample Data")
    st.dataframe(data.head())
    
    st.subheader("Data Visualization")
    st.line_chart(data)
    
    st.success("✅ Data processing and visualization working!")

st.header("Next Steps")
st.write("""
To enable full AI functionality:
1. Install PyTorch: `pip install torch`
2. Restart the system
3. AI models will be automatically loaded
""")
'''
    
    # Save test app
    with open("test_streamlit_app.py", "w") as f:
        f.write(test_app_content)
    
    print("✅ Created test Streamlit app: test_streamlit_app.py")
    print("🚀 To run: streamlit run test_streamlit_app.py")
    
    return True

def main():
    """Main test function."""
    print("🧠 LocalMind AI - Basic System Test")
    print("=" * 60)
    print("Testing system components that work without AI models...")
    
    # Test basic components
    basic_success = test_basic_components()
    
    # Test Streamlit interface
    streamlit_success = test_streamlit_interface()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    if basic_success:
        print("✅ Basic LocalMind system is functional!")
        print("✅ Core components are working correctly")
        
        if streamlit_success:
            print("✅ Web interface is ready")
            print("\n🚀 You can now:")
            print("   1. Run: streamlit run test_streamlit_app.py")
            print("   2. Install PyTorch for full AI functionality")
            print("   3. Use the system for basic data processing")
        
        return True
    else:
        print("❌ LocalMind system has critical issues")
        print("🔧 Please check the installation and dependencies")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
