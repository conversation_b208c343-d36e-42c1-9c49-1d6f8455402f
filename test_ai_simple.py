#!/usr/bin/env python3
"""
Simple test of LocalMind AI core functionality.
"""

import sys
import asyncio
sys.path.insert(0, '.')

async def main():
    """Test core AI functionality."""
    print("🧠 LocalMind AI - Core Functionality Test")
    print("=" * 50)
    
    try:
        # Test 1: Configuration
        print("1️⃣ Testing Configuration...")
        from localmind.core.config import get_config
        config = get_config()
        print(f"✅ Config loaded: {config.data_dir}")
        
        # Test 2: Sentence Transformers
        print("\n2️⃣ Testing Sentence Transformers...")
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        test_texts = [
            "LocalMind AI is working perfectly!",
            "This is a test of the AI system",
            "Hello, how can I help you today?"
        ]
        
        embeddings = model.encode(test_texts)
        print(f"✅ Generated embeddings: {embeddings.shape}")
        
        # Test 3: Vector Store
        print("\n3️⃣ Testing Vector Store...")
        from localmind.knowledge.vector_store import VectorStore
        vector_store = VectorStore()
        await vector_store.initialize()
        print("✅ Vector store initialized")
        
        # Add some test knowledge
        await vector_store.add_text(
            text="LocalMind AI is a revolutionary local AI system",
            content_id="test_knowledge_1",
            content_type="knowledge",
            metadata={"category": "system_info"}
        )
        print("✅ Knowledge added to vector store")
        
        # Search for similar content
        results = await vector_store.search_similar_text(
            query="What is LocalMind AI?",
            limit=3
        )
        print(f"✅ Found {len(results)} similar results")
        
        # Test 4: Knowledge Base
        print("\n4️⃣ Testing Knowledge Base...")
        from localmind.knowledge.knowledge_base import KnowledgeBase
        kb = KnowledgeBase()
        await kb.initialize()
        print("✅ Knowledge base initialized")
        
        # Store knowledge
        await kb.store_knowledge(
            content="LocalMind AI can generate code, answer questions, and learn continuously",
            category="capabilities",
            metadata={"test": True}
        )
        print("✅ Knowledge stored in knowledge base")
        
        # Search knowledge
        search_results = await kb.search_knowledge("LocalMind capabilities")
        print(f"✅ Found {len(search_results)} knowledge entries")
        
        # Test 5: Conversation Manager (without full model)
        print("\n5️⃣ Testing Conversation Manager...")
        from localmind.core.conversation_manager import ConversationManager
        conv_manager = ConversationManager()
        print("✅ Conversation manager created")
        
        # Test 6: AI Engine (basic initialization)
        print("\n6️⃣ Testing AI Engine...")
        from localmind.core.ai_engine import LocalMindEngine
        engine = LocalMindEngine()
        print("✅ AI engine created")
        
        # Summary
        print("\n" + "=" * 50)
        print("🎉 ALL CORE TESTS PASSED!")
        print("✅ LocalMind AI core functionality is working!")
        print("\n📋 Test Results:")
        print("  ✅ Configuration system")
        print("  ✅ Sentence transformers")
        print("  ✅ Vector store")
        print("  ✅ Knowledge base")
        print("  ✅ Conversation manager")
        print("  ✅ AI engine")
        
        print("\n🌐 Next Steps:")
        print("  • Web interface: http://localhost:8000")
        print("  • CLI: python run_localmind.py cli")
        print("  • Full demo: python demo.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 LocalMind AI is ready to use!")
    else:
        print("\n❌ Some issues detected")
    sys.exit(0 if success else 1)
