"""Comprehensive tests for LocalMind AI system."""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path

from localmind.core.ai_engine import LocalMindEngine
from localmind.core.config import Config, set_config
from localmind.interfaces.api import LocalMindAPI
from localmind.knowledge.knowledge_base import KnowledgeBase
from localmind.knowledge.vector_store import VectorStore
from localmind.execution.code_generator import CodeGenerator
from localmind.execution.code_executor import CodeExecutor


@pytest.fixture
async def temp_config():
    """Create a temporary configuration for testing."""
    temp_dir = tempfile.mkdtemp()
    
    config = Config(
        data_dir=temp_dir,
        debug=True,
        database={
            "sqlite_path": f"{temp_dir}/test.db",
            "vector_db_path": f"{temp_dir}/vector_test"
        }
    )
    
    set_config(config)
    yield config
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
async def ai_engine(temp_config):
    """Create an AI engine instance for testing."""
    engine = LocalMindEngine()
    await engine.initialize()
    yield engine
    await engine.shutdown()


@pytest.fixture
async def api_client(temp_config):
    """Create an API client for testing."""
    api = LocalMindAPI()
    await api.initialize()
    yield api
    await api.shutdown()


class TestConfiguration:
    """Test configuration management."""
    
    def test_config_creation(self):
        """Test configuration creation."""
        config = Config()
        assert config.data_dir == "data"
        assert config.log_level == "INFO"
        assert config.debug is False
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = Config(
            model={"temperature": 0.5},
            execution={"timeout_seconds": 60}
        )
        assert config.model.temperature == 0.5
        assert config.execution.timeout_seconds == 60


class TestKnowledgeBase:
    """Test knowledge base functionality."""
    
    @pytest.mark.asyncio
    async def test_knowledge_base_initialization(self, temp_config):
        """Test knowledge base initialization."""
        kb = KnowledgeBase()
        await kb.initialize()
        
        stats = await kb.get_stats()
        assert 'knowledge_entries' in stats
        assert 'code_snippets' in stats
    
    @pytest.mark.asyncio
    async def test_add_knowledge(self, temp_config):
        """Test adding knowledge to the base."""
        kb = KnowledgeBase()
        await kb.initialize()
        
        knowledge_id = await kb.add_knowledge(
            title="Test Knowledge",
            content="This is test content for the knowledge base.",
            category="test",
            tags=["test", "example"]
        )
        
        assert knowledge_id is not None
        
        # Retrieve the knowledge
        knowledge = await kb.get_knowledge_by_id(int(knowledge_id))
        assert knowledge is not None
        assert knowledge['title'] == "Test Knowledge"
        assert knowledge['category'] == "test"
    
    @pytest.mark.asyncio
    async def test_search_knowledge(self, temp_config):
        """Test knowledge search functionality."""
        kb = KnowledgeBase()
        await kb.initialize()
        
        # Add test knowledge
        await kb.add_knowledge(
            title="Python Programming",
            content="Python is a high-level programming language.",
            category="programming"
        )
        
        # Search for knowledge
        results = await kb.search_knowledge("Python programming")
        assert len(results) > 0
        assert any("Python" in result['title'] for result in results)
    
    @pytest.mark.asyncio
    async def test_add_code_snippet(self, temp_config):
        """Test adding code snippets."""
        kb = KnowledgeBase()
        await kb.initialize()
        
        snippet_id = await kb.add_code_snippet(
            title="Hello World",
            code='print("Hello, World!")',
            language="python",
            description="A simple hello world program"
        )
        
        assert snippet_id is not None
        
        # Search for code snippets
        results = await kb.search_code_snippets("hello world", language="python")
        assert len(results) > 0


class TestVectorStore:
    """Test vector store functionality."""
    
    @pytest.mark.asyncio
    async def test_vector_store_initialization(self, temp_config):
        """Test vector store initialization."""
        vs = VectorStore()
        await vs.initialize()
        
        stats = await vs.get_stats()
        assert 'total_embeddings' in stats
    
    @pytest.mark.asyncio
    async def test_add_and_search_text(self, temp_config):
        """Test adding and searching text in vector store."""
        vs = VectorStore()
        await vs.initialize()
        
        # Add text
        content_id = await vs.add_text(
            text="Machine learning is a subset of artificial intelligence.",
            content_id="test_ml_1",
            content_type="knowledge"
        )
        
        assert content_id == "test_ml_1"
        
        # Search for similar text
        results = await vs.search_similar("artificial intelligence", n_results=5)
        assert len(results) > 0
        assert any("machine learning" in result['content'].lower() for result in results)


class TestCodeGeneration:
    """Test code generation functionality."""
    
    @pytest.mark.asyncio
    async def test_code_generator_initialization(self, temp_config):
        """Test code generator initialization."""
        cg = CodeGenerator()
        await cg.initialize()
        # Should not raise any exceptions
    
    @pytest.mark.asyncio
    async def test_generate_python_code(self, temp_config):
        """Test Python code generation."""
        cg = CodeGenerator()
        await cg.initialize()
        
        result = await cg.generate_code(
            description="Create a function that adds two numbers",
            language="python"
        )
        
        assert result['success'] is True
        assert 'code' in result
        assert len(result['code']) > 0
        assert 'def' in result['code'] or 'function' in result['explanation'].lower()
    
    @pytest.mark.asyncio
    async def test_explain_code(self, temp_config):
        """Test code explanation functionality."""
        cg = CodeGenerator()
        await cg.initialize()
        
        code = """
def add_numbers(a, b):
    return a + b
        """
        
        explanation = await cg.explain_code(code, "python")
        assert len(explanation) > 0
        assert "function" in explanation.lower()


class TestCodeExecution:
    """Test code execution functionality."""
    
    @pytest.mark.asyncio
    async def test_code_executor_initialization(self, temp_config):
        """Test code executor initialization."""
        ce = CodeExecutor()
        await ce.initialize()
        
        stats = await ce.get_execution_stats()
        assert 'supported_languages' in stats
    
    @pytest.mark.asyncio
    async def test_execute_python_code(self, temp_config):
        """Test Python code execution."""
        ce = CodeExecutor()
        await ce.initialize()
        
        code = 'print("Hello from LocalMind!")'
        result = await ce.execute_code(code, "python")
        
        assert result['success'] is True
        assert "Hello from LocalMind!" in result['output']
        assert result['execution_time'] > 0
    
    @pytest.mark.asyncio
    async def test_execute_invalid_code(self, temp_config):
        """Test execution of invalid code."""
        ce = CodeExecutor()
        await ce.initialize()
        
        code = 'print("Hello" +'  # Syntax error
        result = await ce.execute_code(code, "python")
        
        assert result['success'] is False
        assert len(result['error']) > 0
    
    @pytest.mark.asyncio
    async def test_code_security_validation(self, temp_config):
        """Test code security validation."""
        ce = CodeExecutor()
        await ce.initialize()
        
        # Test dangerous code
        dangerous_code = 'import os; os.system("rm -rf /")'
        result = await ce.execute_code(dangerous_code, "python")
        
        assert result['success'] is False
        assert 'validation failed' in result['error'].lower()


class TestAIEngine:
    """Test the main AI engine."""
    
    @pytest.mark.asyncio
    async def test_ai_engine_initialization(self, ai_engine):
        """Test AI engine initialization."""
        assert ai_engine.is_initialized is True
    
    @pytest.mark.asyncio
    async def test_chat_functionality(self, ai_engine):
        """Test chat functionality."""
        response = await ai_engine.chat("Hello, how are you?")
        assert isinstance(response, str)
        assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_code_generation_integration(self, ai_engine):
        """Test integrated code generation."""
        result = await ai_engine.generate_code(
            "Create a function that calculates factorial",
            "python"
        )
        
        assert result['success'] is True
        assert 'code' in result
        assert len(result['code']) > 0
    
    @pytest.mark.asyncio
    async def test_session_management(self, ai_engine):
        """Test session save and load."""
        # Send a message
        await ai_engine.chat("Test message for session")
        
        # Save session
        session_name = await ai_engine.save_session("test_session")
        assert session_name is not None
        
        # Clear history
        await ai_engine.clear_conversation_history()
        history = await ai_engine.get_conversation_history()
        assert len(history) == 0
        
        # Load session
        success = await ai_engine.load_session("test_session")
        assert success is True
        
        # Check if history is restored
        history = await ai_engine.get_conversation_history()
        assert len(history) > 0


class TestAPI:
    """Test the API interface."""
    
    @pytest.mark.asyncio
    async def test_api_initialization(self, api_client):
        """Test API initialization."""
        assert api_client._initialized is True
    
    @pytest.mark.asyncio
    async def test_api_chat(self, api_client):
        """Test API chat functionality."""
        response = await api_client.chat("What is Python?")
        assert isinstance(response, str)
        assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_api_code_generation(self, api_client):
        """Test API code generation."""
        result = await api_client.generate_code(
            "Create a simple calculator function",
            "python"
        )
        
        assert 'code' in result
        assert 'success' in result
    
    @pytest.mark.asyncio
    async def test_api_system_stats(self, api_client):
        """Test API system statistics."""
        stats = await api_client.get_system_stats()
        
        assert 'timestamp' in stats
        assert 'system_initialized' in stats
        assert stats['system_initialized'] is True


class TestIntegration:
    """Integration tests for the complete system."""
    
    @pytest.mark.asyncio
    async def test_full_workflow(self, ai_engine):
        """Test a complete workflow from chat to code generation to execution."""
        # Step 1: Chat about programming
        response = await ai_engine.chat("I need help with Python programming")
        assert len(response) > 0
        
        # Step 2: Generate code
        code_result = await ai_engine.generate_code(
            "Create a function that reverses a string",
            "python"
        )
        assert code_result['success'] is True
        
        # Step 3: Execute the generated code
        if code_result['code'].strip():
            exec_result = await ai_engine.execute_code(
                code_result['code'],
                "python"
            )
            # Should either succeed or fail gracefully
            assert 'success' in exec_result
            assert 'execution_time' in exec_result
    
    @pytest.mark.asyncio
    async def test_learning_integration(self, ai_engine):
        """Test learning integration."""
        # Generate some interactions for learning
        await ai_engine.chat("What is machine learning?")
        await ai_engine.generate_code("Create a simple ML example", "python")
        
        # Check learning stats
        learning_stats = await ai_engine.learning_engine.get_learning_stats()
        assert learning_stats['total_interactions'] > 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
