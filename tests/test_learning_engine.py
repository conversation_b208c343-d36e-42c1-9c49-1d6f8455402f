"""Tests for the learning engine."""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path

from localmind.core.learning_engine import LearningEngine
from localmind.knowledge.knowledge_base import KnowledgeBase
from localmind.core.config import Config, set_config


@pytest.fixture
async def temp_config():
    """Create a temporary configuration for testing."""
    temp_dir = tempfile.mkdtemp()
    
    config = Config(
        data_dir=temp_dir,
        debug=True,
        database={
            "sqlite_path": f"{temp_dir}/test.db",
            "vector_db_path": f"{temp_dir}/vector_test"
        }
    )
    
    set_config(config)
    yield config
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
async def learning_engine(temp_config):
    """Create a learning engine instance for testing."""
    kb = KnowledgeBase()
    await kb.initialize()
    
    engine = LearningEngine(kb)
    await engine.initialize()
    
    yield engine
    
    if hasattr(engine, 'shutdown'):
        await engine.shutdown()


class TestLearningEngine:
    """Test learning engine functionality."""
    
    @pytest.mark.asyncio
    async def test_learning_engine_initialization(self, learning_engine):
        """Test learning engine initialization."""
        assert learning_engine._initialized is True
        
        stats = await learning_engine.get_learning_stats()
        assert 'total_interactions' in stats
        assert 'successful_learnings' in stats
    
    @pytest.mark.asyncio
    async def test_learn_from_interaction(self, learning_engine):
        """Test learning from user interactions."""
        result = await learning_engine.learn_from_interaction(
            user_message="What is Python programming?",
            ai_response="Python is a high-level programming language known for its simplicity and readability.",
            context={"session_id": "test_session"}
        )
        
        assert 'learned' in result
        assert 'confidence' in result
        
        # Check that learning stats were updated
        stats = await learning_engine.get_learning_stats()
        assert stats['total_interactions'] > 0
    
    @pytest.mark.asyncio
    async def test_learn_from_code_generation(self, learning_engine):
        """Test learning from code generation."""
        result = await learning_engine.learn_from_code_generation(
            description="Create a function to add two numbers",
            language="python",
            generated_code="def add(a, b):\n    return a + b",
            execution_result={"success": True, "output": "Function created successfully"}
        )
        
        assert 'learned' in result
        assert 'code_snippet_added' in result
    
    @pytest.mark.asyncio
    async def test_learn_from_code_execution(self, learning_engine):
        """Test learning from code execution."""
        result = await learning_engine.learn_from_code_execution(
            code="print('Hello, World!')",
            language="python",
            execution_result={"success": True, "output": "Hello, World!"}
        )
        
        assert 'learned' in result
        assert 'knowledge_updated' in result
    
    @pytest.mark.asyncio
    async def test_pattern_detection(self, learning_engine):
        """Test pattern detection in interactions."""
        # Generate multiple similar interactions
        for i in range(5):
            await learning_engine.learn_from_interaction(
                user_message=f"What is machine learning example {i}?",
                ai_response="Machine learning is a subset of artificial intelligence.",
                context={"session_id": "test_session"}
            )
        
        # The learning engine should detect the pattern
        stats = await learning_engine.get_learning_stats()
        assert stats['total_interactions'] >= 5
    
    @pytest.mark.asyncio
    async def test_learning_parameters(self, learning_engine):
        """Test learning parameter adjustment."""
        # Test setting learning parameters
        await learning_engine.set_learning_parameters(
            learning_threshold=0.9,
            pattern_detection_window=50,
            auto_learning_enabled=False
        )
        
        assert learning_engine.learning_threshold == 0.9
        assert learning_engine.pattern_detection_window == 50
        assert learning_engine.auto_learning_enabled is False
    
    @pytest.mark.asyncio
    async def test_knowledge_extraction(self, learning_engine):
        """Test knowledge extraction from interactions."""
        # Test with a knowledge-seeking question
        result = await learning_engine.learn_from_interaction(
            user_message="What is artificial intelligence?",
            ai_response="Artificial intelligence (AI) is the simulation of human intelligence in machines that are programmed to think and learn like humans.",
            context={"session_id": "test_session"}
        )
        
        # Should extract knowledge for future use
        assert result.get('learned', False) or result.get('knowledge_added', False)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, learning_engine):
        """Test error handling in learning processes."""
        # Test with invalid input
        result = await learning_engine.learn_from_interaction(
            user_message="",  # Empty message
            ai_response="",   # Empty response
            context=None
        )
        
        # Should handle gracefully
        assert 'learned' in result
        assert 'reason' in result


class TestLearningMetrics:
    """Test learning metrics and statistics."""
    
    @pytest.mark.asyncio
    async def test_metric_recording(self, learning_engine):
        """Test recording of learning metrics."""
        # Record some test metrics
        await learning_engine.record_metric("test_metric", 0.85)
        await learning_engine.record_metric("response_time", 1.2)
        
        # Metrics should be recorded
        stats = await learning_engine.get_learning_stats()
        assert isinstance(stats, dict)
    
    @pytest.mark.asyncio
    async def test_learning_statistics(self, learning_engine):
        """Test learning statistics collection."""
        # Generate some learning events
        for i in range(3):
            await learning_engine.learn_from_interaction(
                user_message=f"Test question {i}",
                ai_response=f"Test response {i}",
                context={"test": True}
            )
        
        stats = await learning_engine.get_learning_stats()
        
        assert stats['total_interactions'] >= 3
        assert 'successful_learnings' in stats
        assert 'failed_learnings' in stats


class TestLearningIntegration:
    """Test learning engine integration with other components."""
    
    @pytest.mark.asyncio
    async def test_knowledge_base_integration(self, learning_engine):
        """Test integration with knowledge base."""
        # Test that learning engine can add knowledge
        result = await learning_engine.learn_from_interaction(
            user_message="Explain quantum computing",
            ai_response="Quantum computing is a type of computation that harnesses quantum mechanical phenomena.",
            context={"session_id": "test_session"}
        )
        
        # Check if knowledge was added to the knowledge base
        kb_stats = await learning_engine.knowledge_base.get_stats()
        assert 'knowledge_entries' in kb_stats
    
    @pytest.mark.asyncio
    async def test_code_learning_integration(self, learning_engine):
        """Test code learning integration."""
        # Test learning from successful code generation
        result = await learning_engine.learn_from_code_generation(
            description="Create a hello world function",
            language="python",
            generated_code='def hello():\n    print("Hello, World!")',
            execution_result={"success": True, "output": "Hello, World!"}
        )
        
        assert result.get('learned', False)
        
        # Test learning from failed code generation
        result = await learning_engine.learn_from_code_generation(
            description="Invalid code request",
            language="python",
            generated_code="invalid syntax code",
            execution_result={"success": False, "error": "Syntax error"}
        )
        
        assert result.get('learned', False) or 'reason' in result
