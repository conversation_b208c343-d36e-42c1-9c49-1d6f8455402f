"""Tests for the plugin system."""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path

from localmind.extension.plugin_engine import Plugin<PERSON><PERSON><PERSON>, Plugin
from localmind.core.config import Config, set_config


@pytest.fixture
async def temp_config():
    """Create a temporary configuration for testing."""
    temp_dir = tempfile.mkdtemp()
    
    config = Config(
        data_dir=temp_dir,
        debug=True,
        database={
            "sqlite_path": f"{temp_dir}/test.db",
            "vector_db_path": f"{temp_dir}/vector_test"
        }
    )
    
    set_config(config)
    yield config
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
async def plugin_engine(temp_config):
    """Create a plugin engine instance for testing."""
    engine = PluginEngine()
    await engine.initialize()
    yield engine
    await engine.shutdown()


class TestPlugin(Plugin):
    """Test plugin for testing purposes."""
    
    def __init__(self):
        super().__init__("test_plugin", "1.0.0")
        self.initialized = False
        self.hook_called = False
    
    async def initialize(self):
        """Initialize the test plugin."""
        self.initialized = True
    
    async def shutdown(self):
        """Shutdown the test plugin."""
        self.initialized = False
    
    def get_capabilities(self):
        """Return test capabilities."""
        return ["test_capability", "example_feature"]
    
    async def hook_test_event(self, data):
        """Test hook method."""
        self.hook_called = True
        return {"processed": True, "data": data}


class TestPluginEngine:
    """Test plugin engine functionality."""
    
    @pytest.mark.asyncio
    async def test_plugin_engine_initialization(self, plugin_engine):
        """Test plugin engine initialization."""
        assert plugin_engine._initialized is True
        
        # Should have built-in plugins loaded
        plugins = plugin_engine.list_plugins()
        assert len(plugins) > 0
    
    @pytest.mark.asyncio
    async def test_plugin_registration(self, plugin_engine):
        """Test plugin registration."""
        test_plugin = TestPlugin()
        
        success = await plugin_engine.register_plugin(test_plugin)
        assert success is True
        assert test_plugin.initialized is True
        
        # Check if plugin is in the list
        plugins = plugin_engine.list_plugins()
        plugin_names = [p['name'] for p in plugins]
        assert "test_plugin" in plugin_names
    
    @pytest.mark.asyncio
    async def test_plugin_unregistration(self, plugin_engine):
        """Test plugin unregistration."""
        test_plugin = TestPlugin()
        
        # Register plugin
        await plugin_engine.register_plugin(test_plugin)
        
        # Unregister plugin
        success = await plugin_engine.unregister_plugin("test_plugin")
        assert success is True
        
        # Check if plugin is removed
        plugins = plugin_engine.list_plugins()
        plugin_names = [p['name'] for p in plugins]
        assert "test_plugin" not in plugin_names
    
    @pytest.mark.asyncio
    async def test_plugin_hooks(self, plugin_engine):
        """Test plugin hook system."""
        test_plugin = TestPlugin()
        await plugin_engine.register_plugin(test_plugin)
        
        # Call hook
        results = await plugin_engine.call_hook("test_event", {"test": "data"})
        
        assert len(results) > 0
        assert test_plugin.hook_called is True
        assert results[0]["processed"] is True
    
    @pytest.mark.asyncio
    async def test_plugin_capabilities(self, plugin_engine):
        """Test plugin capability system."""
        test_plugin = TestPlugin()
        await plugin_engine.register_plugin(test_plugin)
        
        capabilities = plugin_engine.get_capabilities()
        assert "test_capability" in capabilities
        assert "example_feature" in capabilities
    
    @pytest.mark.asyncio
    async def test_plugin_enable_disable(self, plugin_engine):
        """Test plugin enable/disable functionality."""
        test_plugin = TestPlugin()
        await plugin_engine.register_plugin(test_plugin)
        
        # Disable plugin
        success = await plugin_engine.disable_plugin("test_plugin")
        assert success is True
        
        plugin = plugin_engine.get_plugin("test_plugin")
        assert plugin.enabled is False
        
        # Enable plugin
        success = await plugin_engine.enable_plugin("test_plugin")
        assert success is True
        assert plugin.enabled is True
    
    @pytest.mark.asyncio
    async def test_plugin_template_creation(self, plugin_engine):
        """Test plugin template creation."""
        template_path = await plugin_engine.create_plugin_template("my_custom_plugin")
        
        assert Path(template_path).exists()
        
        # Check template content
        with open(template_path, 'r') as f:
            content = f.read()
            assert "MyCustomPluginPlugin" in content
            assert "my_custom_plugin" in content


class TestBuiltinPlugins:
    """Test built-in plugins."""
    
    @pytest.mark.asyncio
    async def test_code_analysis_plugin(self, plugin_engine):
        """Test code analysis plugin."""
        # Get the code analysis plugin
        plugin = plugin_engine.get_plugin("code_analysis")
        assert plugin is not None
        
        # Test code analysis hook
        results = await plugin_engine.call_hook(
            "code_generated",
            "Create a function",
            "def test():\n    pass",
            "python"
        )
        
        assert len(results) > 0
        # Should contain analysis results
        analysis = results[0]
        assert "lines_of_code" in analysis
    
    @pytest.mark.asyncio
    async def test_knowledge_enhancement_plugin(self, plugin_engine):
        """Test knowledge enhancement plugin."""
        plugin = plugin_engine.get_plugin("knowledge_enhancement")
        assert plugin is not None
        
        # Test knowledge enhancement hook
        results = await plugin_engine.call_hook(
            "knowledge_added",
            "Test Knowledge",
            "This is test content for knowledge enhancement.",
            "test"
        )
        
        assert len(results) > 0
        enhancement = results[0]
        assert "word_count" in enhancement
        assert "complexity" in enhancement
    
    @pytest.mark.asyncio
    async def test_conversation_analytics_plugin(self, plugin_engine):
        """Test conversation analytics plugin."""
        plugin = plugin_engine.get_plugin("conversation_analytics")
        assert plugin is not None
        
        # Test conversation analysis hook
        results = await plugin_engine.call_hook(
            "after_chat",
            "What is machine learning?",
            "Machine learning is a subset of AI.",
            {"session_id": "test"}
        )
        
        assert len(results) > 0
        analysis = results[0]
        assert "message_length" in analysis
        assert "question_type" in analysis
        assert "sentiment" in analysis
    
    @pytest.mark.asyncio
    async def test_system_monitoring_plugin(self, plugin_engine):
        """Test system monitoring plugin."""
        plugin = plugin_engine.get_plugin("system_monitoring")
        assert plugin is not None
        
        # Test system stats hook
        results = await plugin_engine.call_hook("system_stats")
        
        assert len(results) > 0
        stats = results[0]
        # Should contain system information (may not have psutil in test env)
        assert isinstance(stats, dict)


class TestPluginIntegration:
    """Test plugin integration with the main system."""
    
    @pytest.mark.asyncio
    async def test_plugin_lifecycle(self, plugin_engine):
        """Test complete plugin lifecycle."""
        test_plugin = TestPlugin()
        
        # Register
        success = await plugin_engine.register_plugin(test_plugin)
        assert success is True
        assert test_plugin.initialized is True
        
        # Use plugin
        results = await plugin_engine.call_hook("test_event", {"data": "test"})
        assert len(results) > 0
        
        # Disable
        await plugin_engine.disable_plugin("test_plugin")
        plugin = plugin_engine.get_plugin("test_plugin")
        assert plugin.enabled is False
        
        # Re-enable
        await plugin_engine.enable_plugin("test_plugin")
        assert plugin.enabled is True
        
        # Unregister
        success = await plugin_engine.unregister_plugin("test_plugin")
        assert success is True
    
    @pytest.mark.asyncio
    async def test_multiple_plugins_same_hook(self, plugin_engine):
        """Test multiple plugins responding to the same hook."""
        # Register multiple test plugins
        plugin1 = TestPlugin()
        plugin1.name = "test_plugin_1"
        
        plugin2 = TestPlugin()
        plugin2.name = "test_plugin_2"
        
        await plugin_engine.register_plugin(plugin1)
        await plugin_engine.register_plugin(plugin2)
        
        # Call hook - should get results from both plugins
        results = await plugin_engine.call_hook("test_event", {"data": "test"})
        
        # Should have results from both plugins
        processed_results = [r for r in results if isinstance(r, dict) and r.get("processed")]
        assert len(processed_results) >= 2
    
    @pytest.mark.asyncio
    async def test_plugin_error_handling(self, plugin_engine):
        """Test plugin error handling."""
        class ErrorPlugin(Plugin):
            def __init__(self):
                super().__init__("error_plugin", "1.0.0")
            
            async def hook_test_event(self, data):
                raise Exception("Test error")
        
        error_plugin = ErrorPlugin()
        await plugin_engine.register_plugin(error_plugin)
        
        # Hook call should not fail even if plugin raises error
        results = await plugin_engine.call_hook("test_event", {"data": "test"})
        
        # Should still work (other plugins may have responded)
        assert isinstance(results, list)
