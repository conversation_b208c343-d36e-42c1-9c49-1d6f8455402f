# LocalMind AI System

A completely local AI system with continuous learning, knowledge base development, conversational interface, code generation, and self-extension capabilities.

## System Overview

LocalMind is designed as a modular, extensible AI system that operates entirely locally without requiring internet connectivity. The system features:

- **Continuous Learning**: Persistent knowledge accumulation and capability improvement
- **Local Knowledge Base**: Expanding foundation of knowledge stored locally
- **Natural Conversation**: Human-like dialogue and interaction capabilities
- **Code Generation**: Multi-language code understanding and generation
- **Self-Extension**: Ability to improve and extend its own capabilities
- **Complete Local Operation**: No external dependencies or internet requirements

## Architecture Components

### Core AI Engine
- **Conversation Manager**: Handles dialogue flow and context management
- **Local Language Model**: Core reasoning and language understanding
- **Code Generator**: Programming language processing and generation
- **Learning Engine**: Continuous improvement and knowledge acquisition

### Knowledge Management
- **Knowledge Base**: Structured information storage and retrieval
- **Vector Store**: Semantic search and similarity matching
- **Memory Manager**: Short and long-term memory handling
- **Indexing Engine**: Efficient knowledge organization and access

### Execution Environment
- **Code Executor**: Safe code execution and testing
- **Sandbox Environment**: Isolated execution for security
- **Virtual Machine**: Containerized execution environment

### Self-Extension Framework
- **Model Adapter**: Interface for model updates and improvements
- **Plugin Engine**: Extensible capability system
- **Self-Evaluator**: Performance monitoring and assessment
- **Update Engine**: Automated system improvements

### Storage Layer
- **SQLite Database**: Structured data persistence
- **Vector Database**: Semantic embeddings storage
- **File System**: Document and resource management

## Technology Stack

- **Language**: Python 3.11+
- **ML Framework**: PyTorch, Transformers (Hugging Face)
- **Database**: SQLite, ChromaDB (vector store)
- **Web Framework**: FastAPI (for API), Streamlit (for UI)
- **Code Execution**: Docker, subprocess management
- **Vector Processing**: sentence-transformers, faiss

## Quick Start

### 1. Installation
```bash
# Clone or download the LocalMind system
cd localmind
python install.py
```

The installation script will:
- Check system requirements
- Install all dependencies
- Set up directories and configuration
- Initialize the AI system
- Run basic tests

### 2. Quick Launch
```bash
# Start the web interface (recommended for beginners)
python run_localmind.py web

# Or use the command line interface
python run_localmind.py cli

# Quick chat
python run_localmind.py chat "Hello, how are you?"

# Quick code generation
python run_localmind.py code "Create a function to sort a list"
```

## Detailed Usage

### Web Interface (Recommended)
The web interface provides the most user-friendly experience:

```bash
python run_localmind.py web
# Open browser to http://localhost:8000
```

Features:
- **Chat Interface**: Natural conversation with the AI
- **Code Generation**: Generate and execute code in multiple languages
- **System Statistics**: Monitor performance and usage
- **Session Management**: Save and load conversation sessions

### Command Line Interface
For power users and automation:

```bash
# Interactive mode
python run_localmind.py cli

# Direct commands
python run_localmind.py chat "Explain machine learning"
python run_localmind.py code "Create a sorting algorithm" --language python --execute
python run_localmind.py stats
```

### Python API
For integration with other applications:

```python
import asyncio
from localmind import LocalMindAPI

async def main():
    # Initialize the API
    api = LocalMindAPI()
    await api.initialize()

    # Chat with the AI
    response = await api.chat("Hello, how can you help me?")
    print(response)

    # Generate code
    code_result = await api.generate_code(
        "Create a function that calculates fibonacci numbers",
        language="python"
    )
    print(code_result['code'])

    # Execute code
    if code_result['success']:
        exec_result = await api.execute_code(code_result['code'], "python")
        print(exec_result['output'])

    # Get system statistics
    stats = await api.get_system_stats()
    print(f"Knowledge entries: {stats['knowledge_base']['knowledge_entries']}")

    # Cleanup
    await api.shutdown()

# Run the example
asyncio.run(main())
```

## System Features

### 🧠 Continuous Learning
- **Automatic Knowledge Extraction**: Learns from every interaction
- **Pattern Recognition**: Identifies common questions and improves responses
- **Performance Optimization**: Self-adjusts based on usage patterns
- **Persistent Memory**: Retains knowledge across sessions

### 📚 Knowledge Management
- **Vector-based Search**: Semantic similarity matching for relevant information
- **Categorized Storage**: Organized knowledge base with tags and categories
- **Code Snippet Library**: Reusable code examples with execution statistics
- **Session History**: Complete conversation and interaction logs

### 💻 Code Capabilities
- **Multi-language Support**: Python, JavaScript, Bash with extensible architecture
- **Safe Execution**: Sandboxed environment with security validation
- **Code Analysis**: Quality assessment and improvement suggestions
- **Template Generation**: Smart code templates based on descriptions

### 🔧 Self-Extension Framework
- **Plugin System**: Extensible architecture for new capabilities
- **Performance Monitoring**: Real-time system health and optimization
- **Automatic Updates**: Self-improvement based on performance metrics
- **Capability Assessment**: Continuous evaluation and enhancement

### 🛡️ Security & Privacy
- **Complete Local Operation**: No data leaves your machine
- **Code Validation**: Security checks for generated and executed code
- **Sandboxed Execution**: Isolated environment for code running
- **Data Encryption**: Secure storage of sensitive information

## Configuration

The system is configured through `config.yaml`:

```yaml
# Core settings
data_dir: "data"
log_level: "INFO"
debug: false

# AI model settings
model:
  base_model: "microsoft/DialoGPT-medium"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
  temperature: 0.7
  device: "auto"  # auto, cpu, cuda

# Learning parameters
learning:
  learning_rate: 0.00001
  auto_save_interval: 300
  knowledge_retention_days: 365

# Code execution settings
execution:
  sandbox_enabled: true
  timeout_seconds: 30
  max_memory_mb: 512
  allowed_languages: ["python", "javascript", "bash"]
```

## Advanced Usage

### System Management Scripts

**Backup and Restore:**
```bash
# Create system backup
python scripts/backup_system.py create --compress

# List available backups
python scripts/backup_system.py list

# Restore from backup
python scripts/backup_system.py restore --path backup_file.zip
```

**System Monitoring:**
```bash
# Real-time monitoring
python scripts/system_monitor.py --interval 30

# Generate monitoring report
python scripts/system_monitor.py --duration 3600 --report monitor_report.json

# One-time metrics collection
python scripts/system_monitor.py --once
```

**System Optimization:**
```bash
# Analyze system performance
python scripts/optimize_system.py analyze

# Run full optimization
python scripts/optimize_system.py optimize

# Optimize specific components
python scripts/optimize_system.py database
python scripts/optimize_system.py disk
python scripts/optimize_system.py config
```

### Plugin Development

Create custom plugins to extend LocalMind capabilities:

```bash
# Generate plugin template
python -c "
from localmind.extension.plugin_engine import PluginEngine
import asyncio
async def create():
    engine = PluginEngine()
    await engine.initialize()
    template_path = await engine.create_plugin_template('my_plugin')
    print(f'Plugin template created: {template_path}')
asyncio.run(create())
"

# Edit the generated plugin file
# Place in data/plugins/ directory
# Plugin will be automatically loaded on next startup
```

### Advanced Configuration

**Performance Tuning:**
```yaml
# config.yaml
model:
  device: "cuda"  # Use GPU if available
  max_tokens: 1024  # Reduce for faster responses
  temperature: 0.3  # Lower for more deterministic responses

execution:
  sandbox_enabled: true  # Keep enabled for security
  timeout_seconds: 15  # Reduce for faster timeouts
  max_memory_mb: 256  # Adjust based on system memory

learning:
  learning_rate: 0.0001  # Adjust learning speed
  auto_save_interval: 180  # Save more frequently
```

**Multi-Language Support:**
```yaml
execution:
  allowed_languages:
    - "python"
    - "javascript"
    - "bash"
    - "rust"  # Add more languages as needed
    - "go"
```

## Troubleshooting

### Common Issues

**Installation Problems:**
```bash
# If dependencies fail to install
pip install --upgrade pip
pip install torch --index-url https://download.pytorch.org/whl/cpu
pip install -r requirements.txt

# If models fail to download
python -c "from transformers import AutoTokenizer; AutoTokenizer.from_pretrained('microsoft/DialoGPT-medium')"

# Reset and reinstall
rm -rf data/ venv/
python install.py
```

**Performance Issues:**
```bash
# Check system stats
python run_localmind.py stats

# Run system analysis
python scripts/optimize_system.py analyze

# Optimize system
python scripts/optimize_system.py optimize

# Monitor real-time performance
python scripts/system_monitor.py --interval 10
```

**Memory Issues:**
```bash
# Check memory usage
python scripts/system_monitor.py --once

# Optimize configuration for low memory
python scripts/optimize_system.py config

# Manual configuration adjustments:
# - Set device: "cpu" in config.yaml
# - Reduce max_tokens to 512 or lower
# - Reduce max_memory_mb to 128
# - Clear conversation history regularly
```

**Database Issues:**
```bash
# Optimize database
python scripts/optimize_system.py database

# Backup before major operations
python scripts/backup_system.py create

# Reset database if corrupted
rm data/localmind.db data/vector_store/
python -m localmind.setup --init
```

### Logs and Debugging
- System logs: `data/logs/localmind.log`
- Enable debug mode: set `debug: true` in config.yaml
- Run tests: `python run_localmind.py test`
- Monitor system: `python scripts/system_monitor.py`
- Check configuration: `python scripts/optimize_system.py analyze`

## Contributing

This is a personal AI system project. Contributions and suggestions are welcome through issues and pull requests.

## License

MIT License - See LICENSE file for details.
