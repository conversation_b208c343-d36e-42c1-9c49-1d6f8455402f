
% Default to the notebook output style
((* if not cell_style is defined *))
    ((* set cell_style = 'style_ipython.tex.j2' *))
((* endif *))

% Inherit from the specified cell style.
((* extends cell_style *))


%===============================================================================
% Latex Book
%===============================================================================

((* block predoc *))
    ((( super() )))
    ((* block tableofcontents *))\tableofcontents((* endblock tableofcontents *))
((* endblock predoc *))

((* block docclass *))
\documentclass{report}
((* endblock docclass *))

((* block markdowncell scoped *))
((( cell.source | citation2latex | strip_files_prefix | convert_pandoc('markdown+tex_math_double_backslash', 'json',extra_args=[]) | resolve_references | convert_pandoc('json','latex', extra_args=["--top-level-division=chapter"]) )))
((* endblock markdowncell *))
