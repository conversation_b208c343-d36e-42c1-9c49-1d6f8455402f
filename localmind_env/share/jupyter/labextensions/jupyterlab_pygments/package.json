{"name": "jupyterlab_pygments", "version": "0.3.0", "description": "Pygments theme using JupyterLab CSS variables", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab_pygments", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab_pygments/issues"}, "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "Jupyter Development Team", "email": "<EMAIL>"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,js,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab_pygments.git"}, "scripts": {"build": "jlpm build:css && jlpm build:lib && jlpm build:labextension:dev", "build:css": "python generate_css.py", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc", "build:prod": "jlpm clean && jlpm build:css && jlpm build:lib && jlpm build:labextension", "clean": "jlpm clean:lib", "clean:all": "jlpm clean:lib && jlpm clean:labextension && jlpm clean:lintcache", "clean:labextension": "rimraf jupyterlab_pygments/labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo style/base.css", "clean:lintcache": "rimraf .eslintcache .stylelintcache", "eslint": "jlpm eslint:check --fix", "eslint:check": "eslint . --cache --ext .ts,.tsx", "install:extension": "jlpm build", "lint": "jlpm stylelint && jlpm prettier && jlpm eslint", "lint:check": "jlpm stylelint:check && jlpm prettier:check && jlpm eslint:check", "prettier": "jlpm prettier:base --write --list-different", "prettier:base": "prettier \"**/*{.ts,.tsx,.js,.jsx,.css,.json,.md}\"", "prettier:check": "jlpm prettier:base --check", "stylelint": "jlpm stylelint:check --fix", "stylelint:check": "stylelint --cache \"style/**/*.css\"", "watch": "run-p watch:src watch:labextension", "watch:labextension": "jupyter labextension watch .", "watch:src": "tsc -w"}, "dependencies": {"@jupyterlab/application": "^4.0.8", "@types/node": "^20.9.0"}, "devDependencies": {"@jupyterlab/builder": "^4.0.0", "@types/json-schema": "^7.0.11", "@types/react": "^18.0.26", "@types/react-addons-linked-state-mixin": "^0.14.22", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "css-loader": "^6.7.1", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "npm-run-all": "^4.1.5", "prettier": "3.0.3", "rimraf": "^5.0.5", "source-map-loader": "^1.0.2", "style-loader": "^3.3.1", "stylelint": "^15.10.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^13.0.0", "stylelint-config-standard": "^34.0.0", "stylelint-csstree-validator": "^3.0.0", "stylelint-prettier": "^4.0.0", "typescript": "~5.0.2", "yjs": "^13.5.40"}, "sideEffects": ["style/*.css", "style/index.js"], "styleModule": "style/index.js", "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "outputDir": "jupyterlab_pygments/labextension", "_build": {"load": "static/remoteEntry.5cbb9d2323598fbda535.js", "extension": "./extension", "style": "./style"}}, "jupyter-releaser": {"hooks": {"before-build-npm": ["python -m pip install jupyterlab~=3.1", "jlpm"], "before-build-python": ["jlpm clean:all"]}}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/naming-convention": ["error", {"selector": "interface", "format": ["PascalCase"], "custom": {"regex": "^I[A-Z]", "match": true}}], "@typescript-eslint/no-unused-vars": ["warn", {"args": "none"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": false}], "curly": ["error", "all"], "eqeqeq": "error", "prefer-arrow-callback": "error"}}, "eslintIgnore": ["node_modules", "dist", "coverage", "**/*.d.ts"], "prettier": {"singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "endOfLine": "auto", "overrides": [{"files": "package.json", "options": {"tabWidth": 4}}]}, "stylelint": {"extends": ["stylelint-config-recommended", "stylelint-config-standard", "stylelint-prettier/recommended"], "plugins": ["stylelint-csstree-validator"], "rules": {"csstree/validator": true, "property-no-vendor-prefix": null, "selector-class-pattern": "^([a-z][A-z\\d]*)(-[A-z\\d]+)*$", "selector-no-vendor-prefix": null, "value-no-vendor-prefix": null}}}