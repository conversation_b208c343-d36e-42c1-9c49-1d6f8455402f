var _JUPYTERLAB;(()=>{"use strict";var e,r,t,a,n,o,i,u,d,l,f,s,c,p,b,h,m,g,v,y,j,w,P,S,k,E,_={2345:(e,r,t)=>{var a={"./index":()=>Promise.all([t.e(693),t.e(262),t.e(401),t.e(462),t.e(446)]).then((()=>()=>t(1446))),"./extension":()=>Promise.all([t.e(693),t.e(262),t.e(401),t.e(462),t.e(446)]).then((()=>()=>t(1446)))},n=(e,r)=>(t.R=r,r=t.o(a,e)?a[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),o=(e,r)=>{if(t.S){var a="default",n=t.S[a];if(n&&n!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[a]=e,t.I(a,r)}};t.d(r,{get:()=>n,init:()=>o})}},T={};function x(e){var r=T[e];if(void 0!==r)return r.exports;var t=T[e]={id:e,loaded:!1,exports:{}};return _[e].call(t.exports,t,t.exports,x),t.loaded=!0,t.exports}x.m=_,x.c=T,x.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return x.d(r,{a:r}),r},x.d=(e,r)=>{for(var t in r)x.o(r,t)&&!x.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},x.f={},x.e=e=>Promise.all(Object.keys(x.f).reduce(((r,t)=>(x.f[t](e,r),r)),[])),x.u=e=>(869===e?"@jupyter-widgets/controls":e)+"."+{230:"140efa3d6086bcc30c5f",262:"546d3ae60dd5200eda0c",327:"d242f1a99504b2d5b629",401:"8b529e93be954a2781b0",420:"23ab95819c045f6c36bc",439:"b350310d057b43cdd50f",446:"f8696ce72124c78273da",462:"aefd8335f3ca70a63ba8",586:"085510630436c2e4273f",647:"8458d9c331000024a14a",651:"fe40a967a60b543cf15c",693:"234888600d24ccbba9c6",701:"043aefe0b66133629348",722:"53e4a64671c3c48de007",727:"b26a6f3871012a0fd66a",869:"f54024bed3aaa797fb74",898:"db11fb1a7e18acb58b5b"}[e]+".js?v="+{230:"140efa3d6086bcc30c5f",262:"546d3ae60dd5200eda0c",327:"d242f1a99504b2d5b629",401:"8b529e93be954a2781b0",420:"23ab95819c045f6c36bc",439:"b350310d057b43cdd50f",446:"f8696ce72124c78273da",462:"aefd8335f3ca70a63ba8",586:"085510630436c2e4273f",647:"8458d9c331000024a14a",651:"fe40a967a60b543cf15c",693:"234888600d24ccbba9c6",701:"043aefe0b66133629348",722:"53e4a64671c3c48de007",727:"b26a6f3871012a0fd66a",869:"f54024bed3aaa797fb74",898:"db11fb1a7e18acb58b5b"}[e],x.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),x.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="@jupyter-widgets/jupyterlab-manager:",x.l=(t,a,n,o)=>{if(e[t])e[t].push(a);else{var i,u;if(void 0!==n)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var f=d[l];if(f.getAttribute("src")==t||f.getAttribute("data-webpack")==r+n){i=f;break}}i||(u=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,x.nc&&i.setAttribute("nonce",x.nc),i.setAttribute("data-webpack",r+n),i.src=t),e[t]=[a];var s=(r,a)=>{i.onerror=i.onload=null,clearTimeout(c);var n=e[t];if(delete e[t],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(a))),r)return r(a)},c=setTimeout(s.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=s.bind(null,i.onerror),i.onload=s.bind(null,i.onload),u&&document.head.appendChild(i)}},x.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},x.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{x.S={};var e={},r={};x.I=(t,a)=>{a||(a=[]);var n=r[t];if(n||(n=r[t]={}),!(a.indexOf(n)>=0)){if(a.push(n),e[t])return e[t];x.o(x.S,t)||(x.S[t]={});var o=x.S[t],i="@jupyter-widgets/jupyterlab-manager",u=(e,r,t,a)=>{var n=o[e]=o[e]||{},u=n[r];(!u||!u.loaded&&(!a!=!u.eager?a:i>u.from))&&(n[r]={get:t,from:i,eager:!!a})},d=[];return"default"===t&&(u("@jupyter-widgets/base-manager","1.0.12",(()=>Promise.all([x.e(439),x.e(262),x.e(401),x.e(327)]).then((()=>()=>x(6084))))),u("@jupyter-widgets/base","6.0.11",(()=>Promise.all([x.e(898),x.e(693),x.e(262),x.e(230),x.e(647)]).then((()=>()=>x(909))))),u("@jupyter-widgets/controls","5.0.12",(()=>Promise.all([x.e(727),x.e(693),x.e(401),x.e(462),x.e(230),x.e(586),x.e(701)]).then((()=>()=>x(3586))))),u("@jupyter-widgets/jupyterlab-manager","5.0.15",(()=>Promise.all([x.e(693),x.e(262),x.e(401),x.e(462),x.e(446)]).then((()=>()=>x(1446))))),u("@jupyter-widgets/output","6.0.11",(()=>Promise.all([x.e(401),x.e(420)]).then((()=>()=>x(4420))))),u("jquery","3.7.1",(()=>x.e(651).then((()=>()=>x(4651))))),u("semver","7.6.3",(()=>x.e(722).then((()=>()=>x(2722)))))),e[t]=d.length?Promise.all(d).then((()=>e[t]=1)):1}}})(),(()=>{var e;x.g.importScripts&&(e=x.g.location+"");var r=x.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var a=t.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=t[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),x.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),a=t[1]?r(t[1]):[];return t[2]&&(a.length++,a.push.apply(a,r(t[2]))),t[3]&&(a.push([]),a.push.apply(a,r(t[3]))),a},a=(e,r)=>{e=t(e),r=t(r);for(var a=0;;){if(a>=e.length)return a<r.length&&"u"!=(typeof r[a])[0];var n=e[a],o=(typeof n)[0];if(a>=r.length)return"u"==o;var i=r[a],u=(typeof i)[0];if(o!=u)return"o"==o&&"n"==u||"s"==u||"u"==o;if("o"!=o&&"u"!=o&&n!=i)return n<i;a++}},n=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var a=1,o=1;o<e.length;o++)a--,t+="u"==(typeof(u=e[o]))[0]?"-":(a>0?".":"")+(a=2,u);return t}var i=[];for(o=1;o<e.length;o++){var u=e[o];i.push(0===u?"not("+d()+")":1===u?"("+d()+" || "+d()+")":2===u?i.pop()+" "+i.pop():n(u))}return d();function d(){return i.pop().replace(/^\((.+)\)$/,"$1")}},o=(e,r)=>{if(0 in e){r=t(r);var a=e[0],n=a<0;n&&(a=-a-1);for(var i=0,u=1,d=!0;;u++,i++){var l,f,s=u<e.length?(typeof e[u])[0]:"";if(i>=r.length||"o"==(f=(typeof(l=r[i]))[0]))return!d||("u"==s?u>a&&!n:""==s!=n);if("u"==f){if(!d||"u"!=s)return!1}else if(d)if(s==f)if(u<=a){if(l!=e[u])return!1}else{if(n?l>e[u]:l<e[u])return!1;l!=e[u]&&(d=!1)}else if("s"!=s&&"n"!=s){if(n||u<=a)return!1;d=!1,u--}else{if(u<=a||f<s!=n)return!1;d=!1}else"s"!=s&&"n"!=s&&(d=!1,u--)}}var c=[],p=c.pop.bind(c);for(i=1;i<e.length;i++){var b=e[i];c.push(1==b?p()|p():2==b?p()&p():b?o(b,r):!p())}return!!p()},i=(e,r)=>e&&x.o(e,r),u=e=>(e.loaded=1,e.get()),d=e=>Object.keys(e).reduce(((r,t)=>(e[t].eager&&(r[t]=e[t]),r)),{}),l=(e,r,t)=>{var n=t?d(e[r]):e[r];return(r=Object.keys(n).reduce(((e,r)=>!e||a(e,r)?r:e),0))&&n[r]},f=(e,r,t,n)=>{var i=n?d(e[r]):e[r];return(r=Object.keys(i).reduce(((e,r)=>!o(t,r)||e&&!a(e,r)?e:r),0))&&i[r]},s=(e,r,t)=>{var n=t?d(e[r]):e[r];return Object.keys(n).reduce(((e,r)=>!e||!n[e].loaded&&a(e,r)?r:e),0)},c=(e,r,t,a)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+n(a)+")",p=(e,r,t,a,o)=>{var i=e[t];return"No satisfying version ("+n(a)+")"+(o?" for eager consumption":"")+" of shared module "+t+" found in shared scope "+r+".\nAvailable versions: "+Object.keys(i).map((e=>e+" from "+i[e].from)).join(", ")},b=e=>{throw new Error(e)},h=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},g=(e,r,t)=>t?t():((e,r)=>b("Shared module "+r+" doesn't exist in shared scope "+e))(e,r),v=(m=e=>function(r,t,a,n,o){var i=x.I(r);return i&&i.then&&!a?i.then(e.bind(e,r,x.S[r],t,!1,n,o)):e(r,x.S[r],t,a,n,o)})(((e,r,t,a,n)=>i(r,t)?u(l(r,t,a)):g(e,t,n))),y=m(((e,r,t,a,n,o)=>{if(!i(r,t))return g(e,t,o);var d=f(r,t,n,a);return d?u(d):(h(p(r,e,t,n,a)),u(l(r,t,a)))})),j=m(((e,r,t,a,n,o)=>{if(!i(r,t))return g(e,t,o);var d=f(r,t,n,a);return d?u(d):o?o():void b(p(r,e,t,n,a))})),w=m(((e,r,t,a,n,d)=>{if(!i(r,t))return g(e,t,d);var l=s(r,t,a);return o(n,l)||h(c(r,t,l,n)),u(r[t][l])})),P={},S={5256:()=>w("default","@lumino/widgets",!1,[1,2,3,1,,"alpha",0]),8596:()=>j("default","jquery",!1,[1,3,1,1],(()=>x.e(651).then((()=>()=>x(4651))))),7262:()=>w("default","@lumino/coreutils",!1,[1,2,0,0]),7401:()=>j("default","@jupyter-widgets/base",!1,[1,6,0,11],(()=>Promise.all([x.e(898),x.e(693),x.e(262),x.e(230),x.e(647)]).then((()=>()=>x(909))))),4053:()=>w("default","@lumino/algorithm",!1,[1,2,0,0]),4602:()=>w("default","@lumino/signaling",!1,[1,2,0,0]),632:()=>w("default","@jupyterlab/logconsole",!1,[1,4,4,1]),1113:()=>w("default","@jupyterlab/rendermime",!1,[1,4,4,1]),1378:()=>w("default","@jupyterlab/console",!1,[1,4,4,1]),1489:()=>j("default","@jupyter-widgets/output",!1,[1,6,0,11],(()=>x.e(420).then((()=>()=>x(4420))))),4585:()=>j("default","semver",!1,[1,7,3,5],(()=>x.e(722).then((()=>()=>x(2722))))),4970:()=>w("default","@jupyterlab/notebook",!1,[1,4,4,1]),5828:()=>w("default","@jupyterlab/translation",!1,[1,4,4,1]),7144:()=>w("default","@jupyterlab/services",!1,[1,7,4,1]),7860:()=>y("default","@jupyterlab/outputarea",!1,[1,4,4,1]),8612:()=>w("default","@jupyterlab/settingregistry",!1,[1,4,4,1]),8875:()=>j("default","@jupyter-widgets/base-manager",!1,[1,1,0,12],(()=>Promise.all([x.e(439),x.e(327)]).then((()=>()=>x(6084))))),9012:()=>w("default","@lumino/disposable",!1,[1,2,0,0]),9895:()=>w("default","@jupyterlab/mainmenu",!1,[1,4,4,1]),6230:()=>w("default","@lumino/messaging",!1,[1,2,0,0]),2715:()=>v("default","jquery",!1,(()=>x.e(651).then((()=>()=>x(4651))))),6209:()=>w("default","@lumino/domutils",!1,[1,2,0,0]),5429:()=>j("default","@jupyter-widgets/controls",!1,[1,5,0,12],(()=>Promise.all([x.e(727),x.e(230),x.e(586)]).then((()=>()=>x(3586)))))},k={230:[6230],262:[7262],401:[7401],446:[632,1113,1378,1489,4585,4970,5828,7144,7860,8612,8875,9012,9895],462:[4053,4602],586:[6209],647:[2715],693:[5256,8596],869:[5429]},E={},x.f.consumes=(e,r)=>{x.o(k,e)&&k[e].forEach((e=>{if(x.o(P,e))return r.push(P[e]);if(!E[e]){var t=r=>{P[e]=0,x.m[e]=t=>{delete x.c[e],t.exports=r()}};E[e]=!0;var a=r=>{delete P[e],x.m[e]=t=>{throw delete x.c[e],r}};try{var n=S[e]();n.then?r.push(P[e]=n.then(t).catch(a)):t(n)}catch(e){a(e)}}}))},(()=>{x.b=document.baseURI||self.location.href;var e={250:0};x.f.j=(r,t)=>{var a=x.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else if(/^([24]62|230|401|693|869)$/.test(r))e[r]=0;else{var n=new Promise(((t,n)=>a=e[r]=[t,n]));t.push(a[2]=n);var o=x.p+x.u(r),i=new Error;x.l(o,(t=>{if(x.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var n=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+o+")",i.name="ChunkLoadError",i.type=n,i.request=o,a[1](i)}}),"chunk-"+r,r)}};var r=(r,t)=>{var a,n,[o,i,u]=t,d=0;if(o.some((r=>0!==e[r]))){for(a in i)x.o(i,a)&&(x.m[a]=i[a]);u&&u(x)}for(r&&r(t);d<o.length;d++)n=o[d],x.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunk_jupyter_widgets_jupyterlab_manager=self.webpackChunk_jupyter_widgets_jupyterlab_manager||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),x.nc=void 0;var O=x(2345);(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB)["@jupyter-widgets/jupyterlab-manager"]=O})();