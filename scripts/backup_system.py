#!/usr/bin/env python3
"""
LocalMind AI System Backup Script

Creates a complete backup of the LocalMind system including:
- Database files
- Configuration
- Knowledge base
- Vector store
- Session data
- Logs
"""

import os
import sys
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from localmind.core.config import get_config
from localmind.utils.logging import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


def create_backup(backup_dir: str = None, include_logs: bool = True) -> str:
    """Create a complete system backup."""
    
    if backup_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = f"localmind_backup_{timestamp}"
    
    backup_path = Path(backup_dir)
    backup_path.mkdir(parents=True, exist_ok=True)
    
    config = get_config()
    data_dir = Path(config.data_dir)
    
    logger.info(f"Creating backup in: {backup_path}")
    
    try:
        # Backup database files
        logger.info("Backing up database files...")
        db_backup_dir = backup_path / "database"
        db_backup_dir.mkdir(exist_ok=True)
        
        # SQLite database
        sqlite_path = Path(config.database.sqlite_path)
        if sqlite_path.exists():
            shutil.copy2(sqlite_path, db_backup_dir / sqlite_path.name)
            logger.info(f"Backed up SQLite database: {sqlite_path.name}")
        
        # Vector store
        vector_path = Path(config.database.vector_db_path)
        if vector_path.exists():
            shutil.copytree(vector_path, db_backup_dir / "vector_store", dirs_exist_ok=True)
            logger.info("Backed up vector store")
        
        # Backup configuration
        logger.info("Backing up configuration...")
        config_backup_dir = backup_path / "config"
        config_backup_dir.mkdir(exist_ok=True)
        
        config_files = ["config.yaml", "pyproject.toml", "requirements.txt"]
        for config_file in config_files:
            if Path(config_file).exists():
                shutil.copy2(config_file, config_backup_dir / config_file)
                logger.info(f"Backed up: {config_file}")
        
        # Backup data directory
        logger.info("Backing up data directory...")
        if data_dir.exists():
            data_backup_dir = backup_path / "data"
            
            # Copy specific subdirectories
            subdirs = ["sessions", "knowledge", "plugins", "models"]
            for subdir in subdirs:
                src_path = data_dir / subdir
                if src_path.exists():
                    shutil.copytree(src_path, data_backup_dir / subdir, dirs_exist_ok=True)
                    logger.info(f"Backed up data/{subdir}")
            
            # Backup logs if requested
            if include_logs:
                logs_path = data_dir / "logs"
                if logs_path.exists():
                    shutil.copytree(logs_path, data_backup_dir / "logs", dirs_exist_ok=True)
                    logger.info("Backed up logs")
        
        # Create backup manifest
        logger.info("Creating backup manifest...")
        manifest_path = backup_path / "backup_manifest.txt"
        with open(manifest_path, 'w') as f:
            f.write(f"LocalMind AI System Backup\n")
            f.write(f"Created: {datetime.now().isoformat()}\n")
            f.write(f"System Version: 0.1.0\n")
            f.write(f"Backup Directory: {backup_path.absolute()}\n\n")
            
            f.write("Backup Contents:\n")
            for item in backup_path.rglob("*"):
                if item.is_file():
                    rel_path = item.relative_to(backup_path)
                    size = item.stat().st_size
                    f.write(f"  {rel_path} ({size} bytes)\n")
        
        logger.info(f"Backup completed successfully: {backup_path.absolute()}")
        return str(backup_path.absolute())
        
    except Exception as e:
        logger.error(f"Backup failed: {e}")
        raise


def create_compressed_backup(backup_dir: str = None, include_logs: bool = True) -> str:
    """Create a compressed backup archive."""
    
    # Create regular backup first
    backup_path = create_backup(backup_dir, include_logs)
    
    # Create compressed archive
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_name = f"localmind_backup_{timestamp}.zip"
    
    logger.info(f"Creating compressed archive: {archive_name}")
    
    with zipfile.ZipFile(archive_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        backup_path_obj = Path(backup_path)
        for file_path in backup_path_obj.rglob("*"):
            if file_path.is_file():
                arcname = file_path.relative_to(backup_path_obj.parent)
                zipf.write(file_path, arcname)
    
    # Remove uncompressed backup directory
    shutil.rmtree(backup_path)
    
    archive_path = Path(archive_name).absolute()
    archive_size = archive_path.stat().st_size / (1024 * 1024)  # MB
    
    logger.info(f"Compressed backup created: {archive_path} ({archive_size:.1f} MB)")
    return str(archive_path)


def restore_backup(backup_path: str, target_dir: str = None) -> bool:
    """Restore system from backup."""
    
    backup_path_obj = Path(backup_path)
    
    if not backup_path_obj.exists():
        logger.error(f"Backup path does not exist: {backup_path}")
        return False
    
    if target_dir is None:
        target_dir = "."
    
    target_path = Path(target_dir)
    
    logger.info(f"Restoring backup from: {backup_path}")
    logger.info(f"Restoring to: {target_path.absolute()}")
    
    try:
        # Handle compressed backup
        if backup_path.endswith('.zip'):
            logger.info("Extracting compressed backup...")
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(target_path)
            
            # Find the extracted backup directory
            extracted_dirs = [d for d in target_path.iterdir() if d.is_dir() and d.name.startswith('localmind_backup_')]
            if extracted_dirs:
                backup_path_obj = extracted_dirs[0]
            else:
                logger.error("Could not find extracted backup directory")
                return False
        
        # Restore database files
        logger.info("Restoring database files...")
        db_backup_dir = backup_path_obj / "database"
        if db_backup_dir.exists():
            config = get_config()
            
            # Restore SQLite database
            for db_file in db_backup_dir.glob("*.db"):
                target_db_path = Path(config.database.sqlite_path)
                target_db_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(db_file, target_db_path)
                logger.info(f"Restored database: {db_file.name}")
            
            # Restore vector store
            vector_backup = db_backup_dir / "vector_store"
            if vector_backup.exists():
                target_vector_path = Path(config.database.vector_db_path)
                if target_vector_path.exists():
                    shutil.rmtree(target_vector_path)
                shutil.copytree(vector_backup, target_vector_path)
                logger.info("Restored vector store")
        
        # Restore configuration files
        logger.info("Restoring configuration...")
        config_backup_dir = backup_path_obj / "config"
        if config_backup_dir.exists():
            for config_file in config_backup_dir.iterdir():
                if config_file.is_file():
                    shutil.copy2(config_file, target_path / config_file.name)
                    logger.info(f"Restored config: {config_file.name}")
        
        # Restore data directory
        logger.info("Restoring data directory...")
        data_backup_dir = backup_path_obj / "data"
        if data_backup_dir.exists():
            config = get_config()
            target_data_dir = Path(config.data_dir)
            
            for item in data_backup_dir.iterdir():
                target_item = target_data_dir / item.name
                if item.is_dir():
                    if target_item.exists():
                        shutil.rmtree(target_item)
                    shutil.copytree(item, target_item)
                else:
                    target_item.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_item)
                logger.info(f"Restored data/{item.name}")
        
        logger.info("Backup restored successfully")
        return True
        
    except Exception as e:
        logger.error(f"Restore failed: {e}")
        return False


def list_backups(backup_dir: str = ".") -> list:
    """List available backups."""
    
    backup_path = Path(backup_dir)
    backups = []
    
    # Find backup directories
    for item in backup_path.iterdir():
        if item.is_dir() and item.name.startswith('localmind_backup_'):
            manifest_path = item / "backup_manifest.txt"
            if manifest_path.exists():
                with open(manifest_path, 'r') as f:
                    content = f.read()
                    created_line = [line for line in content.split('\n') if line.startswith('Created:')]
                    created = created_line[0].split(':', 1)[1].strip() if created_line else "Unknown"
                
                backups.append({
                    'path': str(item),
                    'type': 'directory',
                    'created': created,
                    'size': sum(f.stat().st_size for f in item.rglob('*') if f.is_file())
                })
    
    # Find backup archives
    for item in backup_path.glob('localmind_backup_*.zip'):
        backups.append({
            'path': str(item),
            'type': 'archive',
            'created': datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
            'size': item.stat().st_size
        })
    
    return sorted(backups, key=lambda x: x['created'], reverse=True)


def main():
    """Main backup script function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="LocalMind AI System Backup Tool")
    parser.add_argument('action', choices=['create', 'restore', 'list'], help='Action to perform')
    parser.add_argument('--path', help='Backup path (for restore) or backup directory name (for create)')
    parser.add_argument('--target', help='Target directory for restore (default: current directory)')
    parser.add_argument('--compress', action='store_true', help='Create compressed backup')
    parser.add_argument('--no-logs', action='store_true', help='Exclude logs from backup')
    
    args = parser.parse_args()
    
    try:
        if args.action == 'create':
            include_logs = not args.no_logs
            if args.compress:
                backup_path = create_compressed_backup(args.path, include_logs)
            else:
                backup_path = create_backup(args.path, include_logs)
            print(f"✅ Backup created: {backup_path}")
        
        elif args.action == 'restore':
            if not args.path:
                print("❌ Error: --path required for restore")
                sys.exit(1)
            
            success = restore_backup(args.path, args.target)
            if success:
                print("✅ Backup restored successfully")
            else:
                print("❌ Backup restore failed")
                sys.exit(1)
        
        elif args.action == 'list':
            backups = list_backups(args.path or ".")
            if backups:
                print("Available backups:")
                for backup in backups:
                    size_mb = backup['size'] / (1024 * 1024)
                    print(f"  {backup['path']} ({backup['type']}, {size_mb:.1f} MB, {backup['created']})")
            else:
                print("No backups found")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
