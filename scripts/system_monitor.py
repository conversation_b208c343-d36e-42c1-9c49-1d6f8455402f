#!/usr/bin/env python3
"""
LocalMind AI System Monitor

Real-time monitoring script for the LocalMind AI system.
Tracks performance, resource usage, and system health.
"""

import asyncio
import sys
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from localmind.interfaces.api import LocalMindAPI
from localmind.utils.logging import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class SystemMonitor:
    """Real-time system monitor for LocalMind AI."""
    
    def __init__(self, update_interval: int = 30):
        self.api = LocalMindAPI()
        self.update_interval = update_interval
        self.monitoring = False
        self.metrics_history: List[Dict[str, Any]] = []
        self.max_history = 100  # Keep last 100 measurements
    
    async def initialize(self):
        """Initialize the monitor."""
        await self.api.initialize()
        logger.info("System monitor initialized")
    
    async def collect_metrics(self) -> Dict[str, Any]:
        """Collect current system metrics."""
        try:
            # Get system stats from API
            stats = await self.api.get_system_stats()
            
            # Add timestamp and additional metrics
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'system_stats': stats
            }
            
            # Add system resource metrics if available
            try:
                import psutil
                metrics['system_resources'] = {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_usage_percent': psutil.disk_usage('/').percent,
                    'process_count': len(psutil.pids())
                }
            except ImportError:
                metrics['system_resources'] = {'error': 'psutil not available'}
            
            # Add LocalMind-specific metrics
            if 'error' not in stats:
                kb_stats = stats.get('knowledge_base', {})
                learning_stats = stats.get('learning', {})
                conv_stats = stats.get('conversation', {})
                
                metrics['localmind_metrics'] = {
                    'knowledge_entries': kb_stats.get('knowledge_entries', 0),
                    'code_snippets': kb_stats.get('code_snippets', 0),
                    'total_interactions': learning_stats.get('total_interactions', 0),
                    'successful_learnings': learning_stats.get('successful_learnings', 0),
                    'model_loaded': conv_stats.get('model_loaded', False),
                    'current_session': conv_stats.get('current_session_id', 'None')
                }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    async def start_monitoring(self, duration: int = None):
        """Start real-time monitoring."""
        self.monitoring = True
        start_time = time.time()
        
        logger.info(f"Starting system monitoring (update interval: {self.update_interval}s)")
        
        try:
            while self.monitoring:
                # Collect metrics
                metrics = await self.collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep history size manageable
                if len(self.metrics_history) > self.max_history:
                    self.metrics_history = self.metrics_history[-self.max_history:]
                
                # Display current metrics
                self.display_metrics(metrics)
                
                # Check if duration limit reached
                if duration and (time.time() - start_time) >= duration:
                    break
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        finally:
            self.monitoring = False
    
    def display_metrics(self, metrics: Dict[str, Any]):
        """Display current metrics."""
        timestamp = metrics.get('timestamp', 'Unknown')
        
        print(f"\n{'='*60}")
        print(f"LocalMind AI System Monitor - {timestamp}")
        print(f"{'='*60}")
        
        # System resources
        resources = metrics.get('system_resources', {})
        if 'error' not in resources:
            print(f"🖥️  System Resources:")
            print(f"   CPU Usage: {resources.get('cpu_percent', 0):.1f}%")
            print(f"   Memory Usage: {resources.get('memory_percent', 0):.1f}%")
            print(f"   Disk Usage: {resources.get('disk_usage_percent', 0):.1f}%")
            print(f"   Processes: {resources.get('process_count', 0)}")
        
        # LocalMind metrics
        lm_metrics = metrics.get('localmind_metrics', {})
        if lm_metrics:
            print(f"\n🧠 LocalMind Metrics:")
            print(f"   Knowledge Entries: {lm_metrics.get('knowledge_entries', 0)}")
            print(f"   Code Snippets: {lm_metrics.get('code_snippets', 0)}")
            print(f"   Total Interactions: {lm_metrics.get('total_interactions', 0)}")
            print(f"   Successful Learnings: {lm_metrics.get('successful_learnings', 0)}")
            print(f"   Model Loaded: {'✅' if lm_metrics.get('model_loaded') else '❌'}")
            print(f"   Current Session: {lm_metrics.get('current_session', 'None')}")
        
        # System status
        system_stats = metrics.get('system_stats', {})
        if 'error' not in system_stats:
            print(f"\n📊 System Status:")
            print(f"   Initialized: {'✅' if system_stats.get('system_initialized') else '❌'}")
            
            # Learning system
            learning = system_stats.get('learning', {})
            if learning:
                success_rate = 0
                total = learning.get('total_interactions', 0)
                successful = learning.get('successful_learnings', 0)
                if total > 0:
                    success_rate = (successful / total) * 100
                print(f"   Learning Success Rate: {success_rate:.1f}%")
        
        # Error handling
        if 'error' in metrics:
            print(f"\n❌ Error: {metrics['error']}")
    
    def generate_report(self, output_file: str = None) -> str:
        """Generate a monitoring report."""
        if not self.metrics_history:
            return "No metrics data available"
        
        report_data = {
            'report_generated': datetime.now().isoformat(),
            'monitoring_period': {
                'start': self.metrics_history[0].get('timestamp'),
                'end': self.metrics_history[-1].get('timestamp'),
                'total_measurements': len(self.metrics_history)
            },
            'summary': self._calculate_summary(),
            'metrics_history': self.metrics_history
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            logger.info(f"Report saved to: {output_file}")
            return output_file
        else:
            return json.dumps(report_data, indent=2)
    
    def _calculate_summary(self) -> Dict[str, Any]:
        """Calculate summary statistics from metrics history."""
        if not self.metrics_history:
            return {}
        
        summary = {
            'system_health': 'Unknown',
            'average_cpu': 0,
            'average_memory': 0,
            'knowledge_growth': 0,
            'learning_trend': 'Unknown'
        }
        
        try:
            # Calculate averages for system resources
            cpu_values = []
            memory_values = []
            knowledge_values = []
            learning_values = []
            
            for metrics in self.metrics_history:
                resources = metrics.get('system_resources', {})
                if 'error' not in resources:
                    cpu_values.append(resources.get('cpu_percent', 0))
                    memory_values.append(resources.get('memory_percent', 0))
                
                lm_metrics = metrics.get('localmind_metrics', {})
                if lm_metrics:
                    knowledge_values.append(lm_metrics.get('knowledge_entries', 0))
                    learning_values.append(lm_metrics.get('total_interactions', 0))
            
            if cpu_values:
                summary['average_cpu'] = sum(cpu_values) / len(cpu_values)
            if memory_values:
                summary['average_memory'] = sum(memory_values) / len(memory_values)
            
            # Calculate knowledge growth
            if len(knowledge_values) >= 2:
                summary['knowledge_growth'] = knowledge_values[-1] - knowledge_values[0]
            
            # Determine learning trend
            if len(learning_values) >= 2:
                if learning_values[-1] > learning_values[0]:
                    summary['learning_trend'] = 'Increasing'
                elif learning_values[-1] < learning_values[0]:
                    summary['learning_trend'] = 'Decreasing'
                else:
                    summary['learning_trend'] = 'Stable'
            
            # Determine overall system health
            avg_cpu = summary['average_cpu']
            avg_memory = summary['average_memory']
            
            if avg_cpu < 50 and avg_memory < 70:
                summary['system_health'] = 'Good'
            elif avg_cpu < 80 and avg_memory < 85:
                summary['system_health'] = 'Fair'
            else:
                summary['system_health'] = 'Poor'
        
        except Exception as e:
            logger.error(f"Error calculating summary: {e}")
            summary['error'] = str(e)
        
        return summary
    
    async def shutdown(self):
        """Shutdown the monitor."""
        self.monitoring = False
        await self.api.shutdown()
        logger.info("System monitor shutdown")


async def main():
    """Main monitoring function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="LocalMind AI System Monitor")
    parser.add_argument('--interval', '-i', type=int, default=30, help='Update interval in seconds')
    parser.add_argument('--duration', '-d', type=int, help='Monitoring duration in seconds')
    parser.add_argument('--report', '-r', help='Generate report and save to file')
    parser.add_argument('--once', action='store_true', help='Collect metrics once and exit')
    
    args = parser.parse_args()
    
    monitor = SystemMonitor(update_interval=args.interval)
    
    try:
        await monitor.initialize()
        
        if args.once:
            # Collect metrics once
            metrics = await monitor.collect_metrics()
            monitor.display_metrics(metrics)
        else:
            # Start continuous monitoring
            await monitor.start_monitoring(duration=args.duration)
        
        # Generate report if requested
        if args.report:
            report_file = args.report
            if not report_file.endswith('.json'):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                report_file = f"localmind_monitor_report_{timestamp}.json"
            
            monitor.generate_report(report_file)
            print(f"\n📊 Report saved to: {report_file}")
    
    except Exception as e:
        logger.error(f"Monitoring error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        await monitor.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
