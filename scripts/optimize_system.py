#!/usr/bin/env python3
"""
LocalMind AI System Optimization Script

Analyzes and optimizes the LocalMind AI system for better performance.
"""

import asyncio
import sys
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from localmind.interfaces.api import LocalMindAPI
from localmind.core.config import get_config, Config
from localmind.storage.database import DatabaseManager
from localmind.utils.logging import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class SystemOptimizer:
    """System optimization manager for LocalMind AI."""
    
    def __init__(self):
        self.api = LocalMindAPI()
        self.config = get_config()
        self.db_manager = DatabaseManager()
        self.optimization_results = []
    
    async def initialize(self):
        """Initialize the optimizer."""
        await self.api.initialize()
        await self.db_manager.initialize()
        logger.info("System optimizer initialized")
    
    async def analyze_system(self) -> Dict[str, Any]:
        """Analyze system performance and identify optimization opportunities."""
        logger.info("Analyzing system performance...")
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'issues': [],
            'recommendations': [],
            'metrics': {}
        }
        
        try:
            # Get system statistics
            stats = await self.api.get_system_stats()
            analysis['metrics'] = stats
            
            # Analyze database performance
            db_analysis = await self._analyze_database()
            analysis['database'] = db_analysis
            
            # Analyze disk usage
            disk_analysis = await self._analyze_disk_usage()
            analysis['disk_usage'] = disk_analysis
            
            # Analyze memory usage
            memory_analysis = await self._analyze_memory_usage()
            analysis['memory'] = memory_analysis
            
            # Analyze configuration
            config_analysis = await self._analyze_configuration()
            analysis['configuration'] = config_analysis
            
            # Generate recommendations
            analysis['recommendations'] = await self._generate_recommendations(analysis)
            
            logger.info("System analysis completed")
            return analysis
            
        except Exception as e:
            logger.error(f"Error during system analysis: {e}")
            analysis['error'] = str(e)
            return analysis
    
    async def _analyze_database(self) -> Dict[str, Any]:
        """Analyze database performance."""
        try:
            db_stats = await self.db_manager.get_database_stats()
            
            analysis = {
                'size_mb': db_stats.get('database_size_bytes', 0) / (1024 * 1024),
                'table_counts': {},
                'issues': [],
                'recommendations': []
            }
            
            # Extract table counts
            for key, value in db_stats.items():
                if key.endswith('_count'):
                    table_name = key.replace('_count', '')
                    analysis['table_counts'][table_name] = value
            
            # Check for issues
            if analysis['size_mb'] > 1000:  # > 1GB
                analysis['issues'].append("Database size is large (>1GB)")
                analysis['recommendations'].append("Consider cleaning up old data")
            
            # Check for excessive conversation history
            conv_count = analysis['table_counts'].get('conversations', 0)
            if conv_count > 10000:
                analysis['issues'].append(f"Large conversation history ({conv_count} entries)")
                analysis['recommendations'].append("Clean up old conversation data")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing database: {e}")
            return {'error': str(e)}
    
    async def _analyze_disk_usage(self) -> Dict[str, Any]:
        """Analyze disk usage."""
        try:
            data_dir = Path(self.config.data_dir)
            
            analysis = {
                'total_size_mb': 0,
                'directory_sizes': {},
                'issues': [],
                'recommendations': []
            }
            
            if data_dir.exists():
                # Calculate directory sizes
                for item in data_dir.iterdir():
                    if item.is_dir():
                        size = sum(f.stat().st_size for f in item.rglob('*') if f.is_file())
                        size_mb = size / (1024 * 1024)
                        analysis['directory_sizes'][item.name] = size_mb
                        analysis['total_size_mb'] += size_mb
                
                # Check for issues
                if analysis['total_size_mb'] > 5000:  # > 5GB
                    analysis['issues'].append(f"Large data directory ({analysis['total_size_mb']:.1f} MB)")
                    analysis['recommendations'].append("Consider archiving old data")
                
                # Check specific directories
                logs_size = analysis['directory_sizes'].get('logs', 0)
                if logs_size > 500:  # > 500MB
                    analysis['issues'].append(f"Large log files ({logs_size:.1f} MB)")
                    analysis['recommendations'].append("Clean up old log files")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing disk usage: {e}")
            return {'error': str(e)}
    
    async def _analyze_memory_usage(self) -> Dict[str, Any]:
        """Analyze memory usage."""
        try:
            analysis = {
                'system_memory': {},
                'issues': [],
                'recommendations': []
            }
            
            try:
                import psutil
                memory = psutil.virtual_memory()
                analysis['system_memory'] = {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'percent_used': memory.percent
                }
                
                # Check for memory issues
                if memory.percent > 85:
                    analysis['issues'].append(f"High memory usage ({memory.percent:.1f}%)")
                    analysis['recommendations'].append("Consider reducing model size or clearing caches")
                
                if memory.total / (1024**3) < 4:  # < 4GB
                    analysis['issues'].append("Low system memory (<4GB)")
                    analysis['recommendations'].append("Consider using CPU-only mode or smaller models")
                
            except ImportError:
                analysis['system_memory'] = {'error': 'psutil not available'}
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing memory usage: {e}")
            return {'error': str(e)}
    
    async def _analyze_configuration(self) -> Dict[str, Any]:
        """Analyze system configuration."""
        try:
            analysis = {
                'current_config': self.config.model_dump(),
                'issues': [],
                'recommendations': []
            }
            
            # Check model configuration
            if self.config.model.device == "cuda":
                try:
                    import torch
                    if not torch.cuda.is_available():
                        analysis['issues'].append("CUDA configured but not available")
                        analysis['recommendations'].append("Set device to 'cpu' or 'auto'")
                except ImportError:
                    analysis['issues'].append("PyTorch not available for CUDA check")
            
            # Check execution settings
            if self.config.execution.timeout_seconds > 60:
                analysis['issues'].append("High execution timeout may cause hangs")
                analysis['recommendations'].append("Consider reducing timeout_seconds")
            
            if self.config.execution.max_memory_mb > 1024:
                analysis['issues'].append("High memory limit for code execution")
                analysis['recommendations'].append("Consider reducing max_memory_mb")
            
            # Check learning settings
            if self.config.learning.max_memory_size > 50000:
                analysis['issues'].append("Large learning memory size")
                analysis['recommendations'].append("Consider reducing max_memory_size")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing configuration: {e}")
            return {'error': str(e)}
    
    async def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        # Collect all recommendations from sub-analyses
        for section in ['database', 'disk_usage', 'memory', 'configuration']:
            section_data = analysis.get(section, {})
            section_recs = section_data.get('recommendations', [])
            recommendations.extend(section_recs)
        
        # Add general recommendations
        stats = analysis.get('metrics', {})
        if 'error' not in stats:
            kb_stats = stats.get('knowledge_base', {})
            learning_stats = stats.get('learning', {})
            
            # Knowledge base recommendations
            knowledge_count = kb_stats.get('knowledge_entries', 0)
            if knowledge_count < 10:
                recommendations.append("Interact more with the system to build knowledge base")
            
            # Learning recommendations
            total_interactions = learning_stats.get('total_interactions', 0)
            successful_learnings = learning_stats.get('successful_learnings', 0)
            
            if total_interactions > 0:
                success_rate = successful_learnings / total_interactions
                if success_rate < 0.5:
                    recommendations.append("Learning success rate is low - check system configuration")
        
        return list(set(recommendations))  # Remove duplicates
    
    async def optimize_database(self) -> Dict[str, Any]:
        """Optimize database performance."""
        logger.info("Optimizing database...")
        
        result = {
            'action': 'database_optimization',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'details': []
        }
        
        try:
            # Clean up old data
            cleanup_stats = await self.db_manager.cleanup_old_data(days=30)
            result['details'].append(f"Cleaned up old data: {cleanup_stats}")
            
            # Vacuum database (SQLite optimization)
            await self.db_manager.execute_query("VACUUM")
            result['details'].append("Database vacuumed")
            
            # Analyze tables (SQLite optimization)
            await self.db_manager.execute_query("ANALYZE")
            result['details'].append("Database analyzed")
            
            result['success'] = True
            logger.info("Database optimization completed")
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            result['error'] = str(e)
        
        return result
    
    async def optimize_disk_usage(self) -> Dict[str, Any]:
        """Optimize disk usage."""
        logger.info("Optimizing disk usage...")
        
        result = {
            'action': 'disk_optimization',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'details': []
        }
        
        try:
            data_dir = Path(self.config.data_dir)
            
            # Clean up old log files
            logs_dir = data_dir / "logs"
            if logs_dir.exists():
                cutoff_date = datetime.now() - timedelta(days=7)
                cleaned_files = 0
                
                for log_file in logs_dir.glob("*.log*"):
                    if log_file.stat().st_mtime < cutoff_date.timestamp():
                        log_file.unlink()
                        cleaned_files += 1
                
                result['details'].append(f"Cleaned up {cleaned_files} old log files")
            
            # Clean up temporary files
            temp_patterns = ["*.tmp", "*.temp", "*~", ".DS_Store"]
            cleaned_temp = 0
            
            for pattern in temp_patterns:
                for temp_file in data_dir.rglob(pattern):
                    temp_file.unlink()
                    cleaned_temp += 1
            
            if cleaned_temp > 0:
                result['details'].append(f"Cleaned up {cleaned_temp} temporary files")
            
            result['success'] = True
            logger.info("Disk optimization completed")
            
        except Exception as e:
            logger.error(f"Disk optimization failed: {e}")
            result['error'] = str(e)
        
        return result
    
    async def optimize_configuration(self) -> Dict[str, Any]:
        """Optimize system configuration."""
        logger.info("Optimizing configuration...")
        
        result = {
            'action': 'configuration_optimization',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'details': [],
            'changes': []
        }
        
        try:
            changes_made = False
            
            # Check and optimize device setting
            if self.config.model.device == "cuda":
                try:
                    import torch
                    if not torch.cuda.is_available():
                        self.config.model.device = "cpu"
                        result['changes'].append("Changed device from 'cuda' to 'cpu' (CUDA not available)")
                        changes_made = True
                except ImportError:
                    self.config.model.device = "cpu"
                    result['changes'].append("Changed device to 'cpu' (PyTorch not available)")
                    changes_made = True
            
            # Optimize memory settings based on system memory
            try:
                import psutil
                total_memory_gb = psutil.virtual_memory().total / (1024**3)
                
                if total_memory_gb < 8:  # Less than 8GB
                    if self.config.execution.max_memory_mb > 256:
                        self.config.execution.max_memory_mb = 256
                        result['changes'].append("Reduced max_memory_mb to 256 for low-memory system")
                        changes_made = True
                    
                    if self.config.learning.max_memory_size > 5000:
                        self.config.learning.max_memory_size = 5000
                        result['changes'].append("Reduced learning max_memory_size for low-memory system")
                        changes_made = True
            except ImportError:
                pass
            
            # Save configuration if changes were made
            if changes_made:
                self.config.save_to_file("config.yaml")
                result['details'].append("Configuration saved to config.yaml")
            
            result['success'] = True
            logger.info("Configuration optimization completed")
            
        except Exception as e:
            logger.error(f"Configuration optimization failed: {e}")
            result['error'] = str(e)
        
        return result
    
    async def run_full_optimization(self) -> Dict[str, Any]:
        """Run complete system optimization."""
        logger.info("Starting full system optimization...")
        
        optimization_results = {
            'timestamp': datetime.now().isoformat(),
            'analysis': await self.analyze_system(),
            'optimizations': []
        }
        
        # Run individual optimizations
        db_result = await self.optimize_database()
        optimization_results['optimizations'].append(db_result)
        
        disk_result = await self.optimize_disk_usage()
        optimization_results['optimizations'].append(disk_result)
        
        config_result = await self.optimize_configuration()
        optimization_results['optimizations'].append(config_result)
        
        # Calculate overall success
        successful_optimizations = sum(1 for opt in optimization_results['optimizations'] if opt.get('success', False))
        total_optimizations = len(optimization_results['optimizations'])
        
        optimization_results['summary'] = {
            'successful_optimizations': successful_optimizations,
            'total_optimizations': total_optimizations,
            'success_rate': successful_optimizations / total_optimizations if total_optimizations > 0 else 0
        }
        
        logger.info(f"Full optimization completed: {successful_optimizations}/{total_optimizations} successful")
        return optimization_results
    
    async def shutdown(self):
        """Shutdown the optimizer."""
        await self.api.shutdown()
        logger.info("System optimizer shutdown")


async def main():
    """Main optimization function."""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description="LocalMind AI System Optimizer")
    parser.add_argument('action', choices=['analyze', 'optimize', 'database', 'disk', 'config'], 
                       help='Optimization action to perform')
    parser.add_argument('--output', '-o', help='Save results to JSON file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    optimizer = SystemOptimizer()
    
    try:
        await optimizer.initialize()
        
        if args.action == 'analyze':
            result = await optimizer.analyze_system()
            print("📊 System Analysis Results:")
            print(f"Timestamp: {result.get('timestamp')}")
            
            issues = result.get('issues', [])
            if issues:
                print(f"\n⚠️  Issues Found ({len(issues)}):")
                for issue in issues:
                    print(f"  - {issue}")
            
            recommendations = result.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Recommendations ({len(recommendations)}):")
                for rec in recommendations:
                    print(f"  - {rec}")
            
            if not issues and not recommendations:
                print("\n✅ No issues found - system is optimized!")
        
        elif args.action == 'optimize':
            result = await optimizer.run_full_optimization()
            print("🔧 Full System Optimization Results:")
            
            summary = result.get('summary', {})
            print(f"Success Rate: {summary.get('success_rate', 0):.1%}")
            print(f"Successful: {summary.get('successful_optimizations', 0)}/{summary.get('total_optimizations', 0)}")
            
            for opt in result.get('optimizations', []):
                action = opt.get('action', 'Unknown')
                success = '✅' if opt.get('success', False) else '❌'
                print(f"\n{success} {action}:")
                
                for detail in opt.get('details', []):
                    print(f"  - {detail}")
                
                for change in opt.get('changes', []):
                    print(f"  🔄 {change}")
                
                if 'error' in opt:
                    print(f"  ❌ Error: {opt['error']}")
        
        elif args.action == 'database':
            result = await optimizer.optimize_database()
            print("🗃️  Database Optimization:")
            print(f"Success: {'✅' if result.get('success') else '❌'}")
            for detail in result.get('details', []):
                print(f"  - {detail}")
        
        elif args.action == 'disk':
            result = await optimizer.optimize_disk_usage()
            print("💾 Disk Optimization:")
            print(f"Success: {'✅' if result.get('success') else '❌'}")
            for detail in result.get('details', []):
                print(f"  - {detail}")
        
        elif args.action == 'config':
            result = await optimizer.optimize_configuration()
            print("⚙️  Configuration Optimization:")
            print(f"Success: {'✅' if result.get('success') else '❌'}")
            for change in result.get('changes', []):
                print(f"  🔄 {change}")
        
        # Save results if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"\n📄 Results saved to: {args.output}")
        
        # Verbose output
        if args.verbose:
            print(f"\n🔍 Detailed Results:")
            print(json.dumps(result, indent=2))
    
    except Exception as e:
        logger.error(f"Optimization error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        await optimizer.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
