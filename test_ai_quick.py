#!/usr/bin/env python3
"""
Quick test of LocalMind AI functionality.
"""

import sys
import asyncio
sys.path.insert(0, '.')

async def test_ai_functionality():
    """Test basic AI functionality."""
    print("🧠 Testing LocalMind AI Functionality")
    print("=" * 50)
    
    try:
        # Import and initialize
        from localmind.core.ai_engine import LocalMindEngine
        from localmind.core.config import get_config
        
        print("✅ Importing LocalMind components...")
        
        # Get configuration
        config = get_config()
        print(f"✅ Configuration loaded: {config.data_dir}")
        
        # Initialize AI engine
        print("🚀 Initializing AI engine...")
        engine = LocalMindEngine()
        await engine.initialize()
        print("✅ AI engine initialized!")
        
        # Test conversation
        print("\n💬 Testing conversation...")
        test_messages = [
            "Hello! What is LocalMind AI?",
            "Can you explain machine learning?",
            "What can you help me with?"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n🔹 Test {i}: {message}")
            try:
                response = await engine.process_message(message)
                print(f"🤖 Response: {response[:100]}...")
            except Exception as e:
                print(f"⚠️ Response error: {e}")
        
        # Test knowledge storage
        print("\n📚 Testing knowledge storage...")
        try:
            await engine.store_knowledge(
                "LocalMind AI is a local AI system that learns continuously",
                category="system_info"
            )
            print("✅ Knowledge stored successfully!")
        except Exception as e:
            print(f"⚠️ Knowledge storage error: {e}")
        
        # Test knowledge search
        print("\n🔍 Testing knowledge search...")
        try:
            results = await engine.search_knowledge("LocalMind AI")
            print(f"✅ Found {len(results)} knowledge entries")
        except Exception as e:
            print(f"⚠️ Knowledge search error: {e}")
        
        # Test code generation
        print("\n💻 Testing code generation...")
        try:
            code_request = "Generate a simple Python function to calculate fibonacci numbers"
            code_response = await engine.generate_code(code_request, "python")
            print(f"✅ Code generated: {len(code_response)} characters")
            print(f"📝 Code preview: {code_response[:100]}...")
        except Exception as e:
            print(f"⚠️ Code generation error: {e}")
        
        print("\n🎉 LocalMind AI testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_ai():
    """Test simple AI without full model loading."""
    print("\n🔬 Testing Simple AI Components")
    print("=" * 50)
    
    try:
        # Test sentence transformers directly
        from sentence_transformers import SentenceTransformer
        print("✅ Sentence Transformers available")
        
        # Test embedding generation
        model = SentenceTransformer('all-MiniLM-L6-v2')
        test_texts = [
            "LocalMind AI is working",
            "This is a test message",
            "AI system is functional"
        ]
        
        embeddings = model.encode(test_texts)
        print(f"✅ Generated embeddings: {embeddings.shape}")
        
        # Test similarity
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        print(f"✅ Similarity calculation: {similarity:.3f}")
        
        # Test vector storage
        from localmind.knowledge.vector_store import VectorStore
        vector_store = VectorStore()
        await vector_store.initialize()
        print("✅ Vector store initialized")
        
        # Store test embeddings
        await vector_store.store_embedding(
            content_id="test_1",
            content_type="test",
            embedding=embeddings[0].tolist(),
            metadata={"test": True}
        )
        print("✅ Embedding stored")
        
        # Search embeddings
        results = await vector_store.search_similar(embeddings[1].tolist(), limit=5)
        print(f"✅ Found {len(results)} similar embeddings")
        
        print("\n🎉 Simple AI components working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Simple AI error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 LocalMind AI - Quick Functionality Test")
    print("=" * 60)
    
    # Test simple components first
    simple_success = asyncio.run(test_simple_ai())
    
    if simple_success:
        print("\n" + "=" * 60)
        print("🎯 Proceeding to full AI test...")
        
        # Test full AI functionality
        full_success = asyncio.run(test_ai_functionality())
        
        if full_success:
            print("\n" + "=" * 60)
            print("🎉 ALL TESTS PASSED!")
            print("✅ LocalMind AI is fully functional and ready to use!")
            print("\n🌐 Web interface available at: http://localhost:8000")
            print("💻 CLI interface: python run_localmind.py cli")
            print("📖 Full demo: python demo.py")
        else:
            print("\n⚠️ Full AI test had issues, but basic components work")
    else:
        print("\n❌ Basic components have issues")
    
    return simple_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
