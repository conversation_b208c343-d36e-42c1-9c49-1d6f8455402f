# LocalMind AI System Configuration

# Core settings
data_dir: "data"
log_level: "INFO"
debug: false

# Database configuration
database:
  sqlite_path: "data/localmind.db"
  vector_db_path: "data/vector_store"
  backup_interval: 3600  # seconds

# AI model configuration
model:
  base_model: "microsoft/DialoGPT-medium"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
  max_tokens: 2048
  temperature: 0.7
  device: "auto"  # auto, cpu, cuda

# Learning engine configuration
learning:
  learning_rate: 0.00001
  batch_size: 8
  max_memory_size: 10000
  knowledge_retention_days: 365
  auto_save_interval: 300  # seconds

# Code execution configuration
execution:
  sandbox_enabled: true
  timeout_seconds: 30
  max_memory_mb: 512
  allowed_languages:
    - "python"
    - "javascript"
    - "bash"

# Interface configuration
interface:
  web_host: "localhost"
  web_port: 8000
  api_host: "localhost"
  api_port: 8001
  enable_cors: true
